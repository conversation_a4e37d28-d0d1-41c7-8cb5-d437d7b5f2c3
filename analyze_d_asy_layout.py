#!/usr/bin/env python3
import json

def analyze_d_asy_layout():
    """分析d-asy layout的布局特征"""
    with open('data/d-asy copy.json', 'r') as f:
        data = json.load(f)
    
    observations = data['ep_observations'][0]  # 第一轮游戏
    print('=== D-ASY Layout 分析 ===')
    
    # 分析玩家活动范围
    player_positions = set()
    for step in observations:
        for player in step.get('players', []):
            pos = tuple(player['position'])
            player_positions.add(pos)
    
    print('玩家活动范围:', sorted(player_positions))
    
    # 分析物品出现位置
    onion_positions = set()
    soup_positions = set()  # 锅的位置
    dish_positions = set()
    
    for step in observations:
        # 检查玩家手中的物品位置（可能是spawn点）
        for player in step.get('players', []):
            held = player.get('held_object')
            if held:
                pos = tuple(held['position'])
                if held['name'] == 'onion':
                    onion_positions.add(pos)
                elif held['name'] == 'dish':
                    dish_positions.add(pos)
        
        # 检查环境中的物品
        for obj in step.get('objects', []):
            pos = tuple(obj['position'])
            if obj['name'] == 'soup':
                soup_positions.add(pos)
    
    print('洋葱出现位置:', sorted(onion_positions))
    print('汤锅位置:', sorted(soup_positions))
    print('盘子出现位置:', sorted(dish_positions))
    
    # 分析布局边界
    all_x = [pos[0] for pos in player_positions]
    all_y = [pos[1] for pos in player_positions]
    print(f'布局范围: X轴 {min(all_x)}-{max(all_x)}, Y轴 {min(all_y)}-{max(all_y)}')
    
    # 分析特殊位置模式
    # 根据观察数据推断spawn点和关键位置
    
    # 检查第一次拿到洋葱的位置（可能是spawn点）
    onion_spawn_candidates = set()
    for i, step in enumerate(observations):
        if i == 0:
            continue
        prev_step = observations[i-1]
        
        for j, player in enumerate(step.get('players', [])):
            prev_player = prev_step.get('players', [{}])[j] if j < len(prev_step.get('players', [])) else {}
            
            # 如果之前没有物品，现在有洋葱
            if not prev_player.get('held_object') and player.get('held_object', {}).get('name') == 'onion':
                pos = tuple(player['position'])
                onion_spawn_candidates.add(pos)
    
    print('推测洋葱spawn点:', sorted(onion_spawn_candidates))
    
    # 检查第一次拿到盘子的位置
    dish_spawn_candidates = set()
    for i, step in enumerate(observations):
        if i == 0:
            continue
        prev_step = observations[i-1]
        
        for j, player in enumerate(step.get('players', [])):
            prev_player = prev_step.get('players', [{}])[j] if j < len(prev_step.get('players', [])) else {}
            
            # 如果之前没有物品，现在有盘子
            if not prev_player.get('held_object') and player.get('held_object', {}).get('name') == 'dish':
                pos = tuple(player['position'])
                dish_spawn_candidates.add(pos)
    
    print('推测盘子spawn点:', sorted(dish_spawn_candidates))
    
    # 检查汤消失的位置（可能是delivery点）
    delivery_candidates = set()
    for i, step in enumerate(observations):
        if i == 0:
            continue
        prev_step = observations[i-1]
        
        for j, player in enumerate(step.get('players', [])):
            prev_player = prev_step.get('players', [{}])[j] if j < len(prev_step.get('players', [])) else {}
            
            # 如果之前有汤，现在没有了
            if (prev_player.get('held_object', {}).get('name') == 'soup' and 
                not player.get('held_object')):
                pos = tuple(player['position'])
                delivery_candidates.add(pos)
    
    print('推测delivery点:', sorted(delivery_candidates))
    
    return {
        'player_range': sorted(player_positions),
        'onion_spawn': sorted(onion_spawn_candidates),
        'dish_spawn': sorted(dish_spawn_candidates),
        'pot_positions': sorted(soup_positions),
        'delivery_points': sorted(delivery_candidates),
        'layout_bounds': {
            'x_min': min(all_x), 'x_max': max(all_x),
            'y_min': min(all_y), 'y_max': max(all_y)
        }
    }

if __name__ == "__main__":
    layout_info = analyze_d_asy_layout()
    print('\n=== 布局总结 ===')
    print(f"推荐配置:")
    print(f"ITEM_SPAWN_POINTS = {layout_info['onion_spawn']}")
    print(f"DISH_SPAWN_POINTS = {layout_info['dish_spawn']}")  
    print(f"POT_POSITIONS = {layout_info['pot_positions']}")
    print(f"DELIVERY_POINTS = {layout_info['delivery_points']}")
