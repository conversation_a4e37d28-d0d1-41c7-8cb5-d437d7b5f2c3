#!/usr/bin/env python3
import json

def analyze_detailed_interactions():
    with open('test2.json', 'r') as f:
        data = json.load(f)
    
    observations = data['ep_observations'][0]
    actions = data['ep_actions'][0]
    
    # 定义关键位置
    ITEM_SPAWN_POINTS = [(1, 0), (3, 0)]  # 洋葱生成点坐标
    DISH_SPAWN_POINTS = [(2, 3)]          # 盘子生成点坐标
    SERVING_POINTS = [(3, 2)]             # 送餐区坐标  
    COUNTER_POSITIONS = [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (3, 2)]  # 可放置物品的位置
    
    def is_adjacent(pos1, pos2):
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1]) == 1
    
    # 分别统计两个玩家的交互
    player_stats = {
        0: {'valid': [], 'invalid': []},  # Human Keyboard Input
        1: {'valid': [], 'invalid': []}   # Human-aware PPO agent
    }

    valid_interactions = []
    invalid_interactions = []
    
    for timestep_idx, timestep_actions in enumerate(actions):
        for player_idx, action in enumerate(timestep_actions):
            if action == "INTERACT":
                obs = observations[timestep_idx]
                player = obs["players"][player_idx]
                pos = tuple(player["position"])
                held = player.get("held_object")
                
                interaction_info = {
                    'timestep': timestep_idx,
                    'player': player_idx,
                    'position': pos,
                    'held_object': held['name'] if held else None,
                    'reason': ''
                }
                
                is_valid = False
                
                # 1. 检查灶台交互
                for obj in obs.get("objects", []):
                    if obj["name"] == "soup" and is_adjacent(pos, tuple(obj["position"])):
                        soup_state = obj["state"]
                        if held and held["name"] == "onion":
                            if soup_state[1] < 3:
                                is_valid = True
                                interaction_info['reason'] = f'放洋葱到锅里 (锅内洋葱数: {soup_state[1]})'
                            else:
                                interaction_info['reason'] = f'尝试放洋葱到满锅 (锅内洋葱数: {soup_state[1]})'
                        elif held and held["name"] == "dish":
                            if soup_state[2] == 20:
                                is_valid = True
                                interaction_info['reason'] = f'从锅里取汤 (烹饪时间: {soup_state[2]})'
                            else:
                                interaction_info['reason'] = f'尝试取未完成的汤 (烹饪时间: {soup_state[2]})'
                        else:
                            interaction_info['reason'] = '在灶台附近无效操作'
                        break
                
                # 2. 检查物品拾取
                if not is_valid and not held:
                    if pos in ITEM_SPAWN_POINTS:
                        is_valid = True
                        interaction_info['reason'] = '拾取洋葱'
                    elif pos in DISH_SPAWN_POINTS:
                        is_valid = True
                        interaction_info['reason'] = '拾取盘子'
                
                # 3. 检查订单提交
                if not is_valid and held and held["name"] == "soup" and pos in SERVING_POINTS:
                    is_valid = True
                    interaction_info['reason'] = '提交汤订单'
                
                # 4. 检查放下物品
                if not is_valid and held and pos in COUNTER_POSITIONS:
                    # 确保目标位置没有其他物品
                    position_occupied = False
                    for obj in obs.get("objects", []):
                        if tuple(obj["position"]) == pos:
                            position_occupied = True
                            break
                    
                    if not position_occupied:
                        is_valid = True
                        interaction_info['reason'] = f'在台面放下{held["name"]}'
                    else:
                        interaction_info['reason'] = f'尝试在有物品的位置放下{held["name"]}'
                
                # 默认情况
                if not interaction_info['reason']:
                    interaction_info['reason'] = '无效交互（对空气或墙壁）'
                
                if is_valid:
                    valid_interactions.append(interaction_info)
                    player_stats[player_idx]['valid'].append(interaction_info)
                else:
                    invalid_interactions.append(interaction_info)
                    player_stats[player_idx]['invalid'].append(interaction_info)
    
    print("=== 详细交互分析报告 ===")
    print(f"总交互次数: {len(valid_interactions) + len(invalid_interactions)}")
    print(f"有效交互次数: {len(valid_interactions)}")
    print(f"无效交互次数: {len(invalid_interactions)}")
    print(f"有效交互比例: {len(valid_interactions) / (len(valid_interactions) + len(invalid_interactions)) * 100:.1f}%")

    # 分玩家统计
    print("\n=== 分玩家统计 ===")
    for player_idx in [0, 1]:
        player_name = "Human Keyboard Input" if player_idx == 0 else "Human-aware PPO agent"
        p_valid = len(player_stats[player_idx]['valid'])
        p_invalid = len(player_stats[player_idx]['invalid'])
        p_total = p_valid + p_invalid

        print(f"\nPlayer{player_idx} ({player_name}):")
        print(f"  有效交互次数: {p_valid}")
        print(f"  无效交互次数: {p_invalid}")
        print(f"  总交互次数: {p_total}")
        print(f"  有效交互比例: {p_valid / p_total * 100:.1f}%" if p_total > 0 else "  无交互数据")
    
    print("\n=== 有效交互详情 ===")
    for i, interaction in enumerate(valid_interactions, 1):
        print(f"{i:2d}. 时间步{interaction['timestep']:3d}, 玩家{interaction['player']}, 位置{interaction['position']}, "
              f"手持:{interaction['held_object'] or '无'}, 操作:{interaction['reason']}")
    
    print("\n=== 无效交互详情 ===")
    for i, interaction in enumerate(invalid_interactions, 1):
        print(f"{i:2d}. 时间步{interaction['timestep']:3d}, 玩家{interaction['player']}, 位置{interaction['position']}, "
              f"手持:{interaction['held_object'] or '无'}, 原因:{interaction['reason']}")
    
    # 统计交互类型
    valid_types = {}
    invalid_types = {}
    
    for interaction in valid_interactions:
        reason = interaction['reason']
        valid_types[reason] = valid_types.get(reason, 0) + 1
    
    for interaction in invalid_interactions:
        reason = interaction['reason']
        invalid_types[reason] = invalid_types.get(reason, 0) + 1
    
    print("\n=== 有效交互类型统计 ===")
    for reason, count in valid_types.items():
        print(f"{reason}: {count}次")
    
    print("\n=== 无效交互类型统计 ===")
    for reason, count in invalid_types.items():
        print(f"{reason}: {count}次")

if __name__ == "__main__":
    analyze_detailed_interactions()
