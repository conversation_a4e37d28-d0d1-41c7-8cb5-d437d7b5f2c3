#!/usr/bin/env python3
"""
详细的单文件分析工具
生成更丰富的输出和报告
"""
import sys
import os
import json
import csv
from datetime import datetime
from analyze_universal import load_data, detect_layout, get_layout_config, analyze_interactions, extract_rewards

def analyze_single_file_detailed(filename: str):
    """详细分析单个文件"""
    print(f"=== 📊 详细单文件分析工具 ===")
    print(f"分析文件: {filename}")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"❌ 错误：文件 {filename} 不存在")
        return
    
    # 加载数据
    print("📁 正在加载数据...")
    data = load_data(filename)
    if not data:
        print("❌ 无法加载数据文件")
        return
    
    # 基本信息
    print("✅ 数据加载成功")
    print(f"📋 数据结构信息:")
    print(f"  - 主要键: {list(data.keys())}")
    
    observations = data.get('ep_observations', [])
    actions = data.get('ep_actions', [])
    rewards = data.get('ep_rewards', [])
    
    print(f"  - 游戏轮数: {len(observations)}")
    if observations and observations[0]:
        print(f"  - 每轮步数: {len(observations[0])}")
        print(f"  - 玩家数量: {len(observations[0][0].get('players', []))}")
    
    # 检测layout
    print(f"\n🔍 Layout检测:")
    layout_type = detect_layout(observations, filename)
    config = get_layout_config(layout_type)
    
    print(f"  - 检测到的Layout类型: {layout_type}")
    if layout_type != 'unknown':
        print(f"  - 洋葱spawn点: {config['ITEM_SPAWN_POINTS']}")
        print(f"  - 盘子spawn点: {config['DISH_SPAWN_POINTS']}")
        print(f"  - 锅位置: {config['POT_POSITIONS']}")
        print(f"  - 送餐点: {config['SERVING_POINTS']}")
    
    # 分析交互
    print(f"\n⚡ 交互分析:")
    interaction_stats = analyze_interactions(observations, actions, layout_type)
    
    # 总体统计
    total_valid = interaction_stats['total']['valid']
    total_invalid = interaction_stats['total']['invalid']
    total_interactions = total_valid + total_invalid
    total_efficiency = (total_valid / total_interactions * 100) if total_interactions > 0 else 0
    
    print(f"  📊 总体统计:")
    print(f"    - 总交互次数: {total_interactions:,}")
    print(f"    - 有效交互: {total_valid:,}")
    print(f"    - 无效交互: {total_invalid:,}")
    print(f"    - 交互效率: {total_efficiency:.2f}%")
    
    # 玩家详细统计
    players_info = [
        ("Player0 (Human Keyboard Input)", interaction_stats['player0']),
        ("Player1 (Human-aware PPO agent)", interaction_stats['player1'])
    ]
    
    for player_name, player_stats in players_info:
        p_valid = player_stats['valid']
        p_invalid = player_stats['invalid']
        p_total = p_valid + p_invalid
        p_efficiency = (p_valid / p_total * 100) if p_total > 0 else 0
        
        print(f"\n  👤 {player_name}:")
        print(f"    - 总交互次数: {p_total:,}")
        print(f"    - 有效交互: {p_valid:,}")
        print(f"    - 无效交互: {p_invalid:,}")
        print(f"    - 交互效率: {p_efficiency:.2f}%")
        print(f"    - 占总交互比例: {(p_total/total_interactions*100):.1f}%")
    
    # 得分分析
    print(f"\n🏆 得分分析:")
    rewards_list = extract_rewards(data)
    if rewards_list:
        total_score = sum(rewards_list)
        avg_score = total_score / len(rewards_list)
        print(f"  - 游戏轮数: {len(rewards_list)}")
        print(f"  - 总得分: {total_score}")
        print(f"  - 平均得分: {avg_score:.2f}")
        print(f"  - 最高得分: {max(rewards_list)}")
        print(f"  - 最低得分: {min(rewards_list)}")
        if len(rewards_list) > 1:
            print(f"  - 得分列表: {rewards_list}")
    else:
        print("  - 未找到得分数据")
        total_score = 0
        avg_score = 0
    
    # 效率分析
    print(f"\n📈 效率分析:")
    if total_interactions > 0:
        interactions_per_score = total_interactions / total_score if total_score > 0 else 0
        valid_per_score = total_valid / total_score if total_score > 0 else 0
        
        print(f"  - 每分需要交互次数: {interactions_per_score:.2f}")
        print(f"  - 每分有效交互次数: {valid_per_score:.2f}")
        print(f"  - 交互效率评级: ", end="")
        
        if total_efficiency >= 80:
            print("🌟 优秀")
        elif total_efficiency >= 60:
            print("👍 良好") 
        elif total_efficiency >= 40:
            print("⚠️ 一般")
        else:
            print("❌ 需要改进")
    
    # 保存详细报告
    base_name = os.path.splitext(os.path.basename(filename))[0]
    
    # 保存CSV报告
    csv_filename = f"detailed_report_{base_name}.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = [
            '分析时间', '文件名', 'Layout类型', 
            '总交互次数', '有效交互次数', '无效交互次数', '总体效率',
            'P0交互次数', 'P0有效交互', 'P0无效交互', 'P0效率',
            'P1交互次数', 'P1有效交互', 'P1无效交互', 'P1效率',
            '游戏轮数', '总得分', '平均得分', '每分交互次数', '每分有效交互'
        ]
        
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        p0_stats = interaction_stats['player0']
        p1_stats = interaction_stats['player1']
        
        row_data = {
            '分析时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '文件名': filename,
            'Layout类型': layout_type,
            '总交互次数': total_interactions,
            '有效交互次数': total_valid,
            '无效交互次数': total_invalid,
            '总体效率': round(total_efficiency, 2),
            'P0交互次数': p0_stats['valid'] + p0_stats['invalid'],
            'P0有效交互': p0_stats['valid'],
            'P0无效交互': p0_stats['invalid'],
            'P0效率': round((p0_stats['valid'] / (p0_stats['valid'] + p0_stats['invalid']) * 100) if (p0_stats['valid'] + p0_stats['invalid']) > 0 else 0, 2),
            'P1交互次数': p1_stats['valid'] + p1_stats['invalid'],
            'P1有效交互': p1_stats['valid'],
            'P1无效交互': p1_stats['invalid'],
            'P1效率': round((p1_stats['valid'] / (p1_stats['valid'] + p1_stats['invalid']) * 100) if (p1_stats['valid'] + p1_stats['invalid']) > 0 else 0, 2),
            '游戏轮数': len(rewards_list) if rewards_list else 0,
            '总得分': total_score,
            '平均得分': round(avg_score, 2),
            '每分交互次数': round(total_interactions / total_score if total_score > 0 else 0, 2),
            '每分有效交互': round(total_valid / total_score if total_score > 0 else 0, 2)
        }
        
        writer.writerow(row_data)
    
    # 保存文本报告
    txt_filename = f"detailed_report_{base_name}.txt"
    with open(txt_filename, 'w', encoding='utf-8') as txtfile:
        txtfile.write(f"详细分析报告\n")
        txtfile.write(f"=" * 50 + "\n")
        txtfile.write(f"文件名: {filename}\n")
        txtfile.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        txtfile.write(f"Layout类型: {layout_type}\n\n")
        
        txtfile.write(f"总体统计:\n")
        txtfile.write(f"  总交互次数: {total_interactions:,}\n")
        txtfile.write(f"  有效交互: {total_valid:,}\n")
        txtfile.write(f"  无效交互: {total_invalid:,}\n")
        txtfile.write(f"  交互效率: {total_efficiency:.2f}%\n\n")
        
        for player_name, player_stats in players_info:
            p_valid = player_stats['valid']
            p_invalid = player_stats['invalid']
            p_total = p_valid + p_invalid
            p_efficiency = (p_valid / p_total * 100) if p_total > 0 else 0
            
            txtfile.write(f"{player_name}:\n")
            txtfile.write(f"  总交互次数: {p_total:,}\n")
            txtfile.write(f"  有效交互: {p_valid:,}\n")
            txtfile.write(f"  无效交互: {p_invalid:,}\n")
            txtfile.write(f"  交互效率: {p_efficiency:.2f}%\n\n")
        
        if rewards_list:
            txtfile.write(f"得分统计:\n")
            txtfile.write(f"  总得分: {total_score}\n")
            txtfile.write(f"  平均得分: {avg_score:.2f}\n")
            txtfile.write(f"  游戏轮数: {len(rewards_list)}\n")
    
    print(f"\n💾 输出文件:")
    print(f"  📊 详细CSV报告: {csv_filename}")
    print(f"  📄 文本报告: {txt_filename}")
    print(f"  🖥️  控制台输出: 完整分析结果")
    
    print(f"\n🎉 分析完成！")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = input("请输入要分析的JSON文件路径: ")
    
    analyze_single_file_detailed(filename)

if __name__ == "__main__":
    main()
