#!/usr/bin/env python3
import json

with open('test2.json', 'r') as f:
    data = json.load(f)

rewards = data['ep_rewards'][0]  # 第一轮的奖励
print('奖励数组长度:', len(rewards))
print('非零奖励:', [r for r in rewards if r != 0])
print('非零奖励位置:', [i for i, r in enumerate(rewards) if r != 0])
print('总得分:', sum(rewards))
print('奖励统计:')
print('  20分奖励次数:', rewards.count(20))
print('  其他奖励:', [r for r in rewards if r != 0 and r != 20])
