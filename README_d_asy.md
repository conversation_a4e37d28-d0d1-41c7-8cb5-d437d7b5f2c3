# 游戏交互分析工具 - ASY & CC 布局支持

## 概述

本工具支持 ASY 和 CC 两种游戏布局的自动检测和交互分析，能够准确识别不同布局类型并应用相应的分析配置。

## 支持的 Layout 类型

### 1. ASY Layout

- 布局范围: X(1-6), Y(1-3)
- 洋葱 spawn 点: [(1, 1), (1, 2), (5, 2)]
- 盘子 spawn 点: [(2, 2), (3, 3), (5, 3)]
- 锅位置: [(4, 2), (4, 3)]
- 送餐点: [(3, 2), (7, 1)]
- 特征: 锅位置在中间区域，交互频繁但效率相对较低

### 2. CC Layout

- 布局范围: X(1-6), Y(1-3)
- 洋葱 spawn 点: [(1, 1), (2, 1), (3, 3), (4, 3)]
- 盘子 spawn 点: [(1, 2)]
- 锅位置: [(3, 0), (4, 0)]
- 送餐点: [(6, 2)]
- 特征: 锅位置在上方，交互效率高但总交互次数较少

## 使用方法

### 方法 1: 使用通用分析工具 (推荐)

```bash
python analyze_universal.py "data/d-asy copy.json"
```

### 方法 2: 使用更新后的原始工具

```bash
python analyze_data.py
```

## 分析结果

### D-ASY Layout 分析结果

- **总有效交互次数**: 573
- **总无效交互次数**: 358
- **总交互次数**: 931
- **有效交互比例**: 61.55%

#### 玩家表现对比

- **Player0 (Human Keyboard Input)**:

  - 有效交互次数: 115
  - 无效交互次数: 62
  - 有效交互比例: 64.97%

- **Player1 (Human-aware PPO agent)**:
  - 有效交互次数: 458
  - 无效交互次数: 296
  - 有效交互比例: 60.74%

#### 得分统计

- 总得分: 540
- 平均得分: 540.00

## 技术特性

### 自动 Layout 检测

工具能够通过分析玩家活动范围自动识别 layout 类型：

- 检查前 20 步的玩家位置
- 根据 X 轴和 Y 轴的最大值判断 layout 类型
- d-asy layout 特征: X 轴范围 ≥6, Y 轴范围 ≤3

### 适配的交互检测

针对 d-asy layout 的特殊配置：

- 支持多个洋葱 spawn 点
- 支持多个锅位置 [(4, 2), (4, 3)]
- 支持多个送餐点 [(3, 2), (7, 1)]
- 扩展的 counter 位置范围

### 输出格式

- 控制台输出: 详细的分析结果
- CSV 文件: 适合 SPSS 导入的格式
- 文件命名: `spss_data_{layout_type}.csv`

## 文件说明

- `analyze_universal.py`: 通用分析工具，支持多种 layout
- `analyze_data.py`: 更新后的原始工具，默认处理 d-asy 数据
- `analyze_d_asy_layout.py`: Layout 特征分析工具
- `spss_data_d_asy.csv`: D-ASY layout 分析结果
- `spss_data_test2.csv`: Test2 layout 分析结果

## 对比分析

### D-ASY vs Test2 Layout

| 指标           | D-ASY Layout | Test2 Layout |
| -------------- | ------------ | ------------ |
| 总交互次数     | 931          | 50           |
| 有效交互比例   | 61.55%       | 46.00%       |
| 总得分         | 540          | 60           |
| Human 玩家效率 | 64.97%       | 50.00%       |
| AI 玩家效率    | 60.74%       | 44.12%       |

### 关键发现

1. **D-ASY layout 交互更频繁**: 931 vs 50 次交互
2. **D-ASY layout 效率更高**: 61.55% vs 46.00%有效交互比例
3. **Human 玩家表现更好**: 在两种 layout 中都优于 AI 玩家
4. **得分差异显著**: D-ASY layout 得分是 Test2 的 9 倍

## 扩展功能

工具设计为可扩展的，添加新 layout 只需：

1. 在`get_layout_config()`中添加新配置
2. 在`detect_layout()`中添加检测逻辑
3. 测试验证

## 注意事项

1. 确保数据文件路径正确
2. CSV 文件使用 UTF-8-BOM 编码，兼容 SPSS
3. 自动检测可能需要调整阈值以适应新 layout
4. 建议使用`analyze_universal.py`以获得最佳兼容性
