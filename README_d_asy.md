# D-ASY Layout 游戏交互分析工具

## 概述

本工具已成功适配d-asy layout，能够自动检测不同的游戏布局并进行相应的交互分析。

## 支持的Layout类型

### 1. test2 Layout (原始)
- 布局范围: X(1-3), Y(1-2) 
- 洋葱spawn点: [(1, 0), (3, 0)]
- 盘子spawn点: [(2, 3)]
- 锅位置: [(2, 0)]
- 送餐点: [(3, 2)]

### 2. d-asy Layout (新增)
- 布局范围: X(1-7), Y(1-3)
- 洋葱spawn点: [(1, 1), (1, 2), (5, 2)]
- 盘子spawn点: [(3, 3), (5, 3)]
- 锅位置: [(4, 2), (4, 3)]
- 送餐点: [(3, 2), (7, 1)]

## 使用方法

### 方法1: 使用通用分析工具 (推荐)
```bash
python analyze_universal.py "data/d-asy copy.json"
```

### 方法2: 使用更新后的原始工具
```bash
python analyze_data.py
```

## 分析结果

### D-ASY Layout 分析结果
- **总有效交互次数**: 573
- **总无效交互次数**: 358
- **总交互次数**: 931
- **有效交互比例**: 61.55%

#### 玩家表现对比
- **Player0 (Human Keyboard Input)**:
  - 有效交互次数: 115
  - 无效交互次数: 62
  - 有效交互比例: 64.97%

- **Player1 (Human-aware PPO agent)**:
  - 有效交互次数: 458
  - 无效交互次数: 296
  - 有效交互比例: 60.74%

#### 得分统计
- 总得分: 540
- 平均得分: 540.00

## 技术特性

### 自动Layout检测
工具能够通过分析玩家活动范围自动识别layout类型：
- 检查前20步的玩家位置
- 根据X轴和Y轴的最大值判断layout类型
- d-asy layout特征: X轴范围≥6, Y轴范围≤3

### 适配的交互检测
针对d-asy layout的特殊配置：
- 支持多个洋葱spawn点
- 支持多个锅位置 [(4, 2), (4, 3)]
- 支持多个送餐点 [(3, 2), (7, 1)]
- 扩展的counter位置范围

### 输出格式
- 控制台输出: 详细的分析结果
- CSV文件: 适合SPSS导入的格式
- 文件命名: `spss_data_{layout_type}.csv`

## 文件说明

- `analyze_universal.py`: 通用分析工具，支持多种layout
- `analyze_data.py`: 更新后的原始工具，默认处理d-asy数据
- `analyze_d_asy_layout.py`: Layout特征分析工具
- `spss_data_d_asy.csv`: D-ASY layout分析结果
- `spss_data_test2.csv`: Test2 layout分析结果

## 对比分析

### D-ASY vs Test2 Layout
| 指标 | D-ASY Layout | Test2 Layout |
|------|-------------|-------------|
| 总交互次数 | 931 | 50 |
| 有效交互比例 | 61.55% | 46.00% |
| 总得分 | 540 | 60 |
| Human玩家效率 | 64.97% | 50.00% |
| AI玩家效率 | 60.74% | 44.12% |

### 关键发现
1. **D-ASY layout交互更频繁**: 931 vs 50次交互
2. **D-ASY layout效率更高**: 61.55% vs 46.00%有效交互比例
3. **Human玩家表现更好**: 在两种layout中都优于AI玩家
4. **得分差异显著**: D-ASY layout得分是Test2的9倍

## 扩展功能

工具设计为可扩展的，添加新layout只需：
1. 在`get_layout_config()`中添加新配置
2. 在`detect_layout()`中添加检测逻辑
3. 测试验证

## 注意事项

1. 确保数据文件路径正确
2. CSV文件使用UTF-8-BOM编码，兼容SPSS
3. 自动检测可能需要调整阈值以适应新layout
4. 建议使用`analyze_universal.py`以获得最佳兼容性
