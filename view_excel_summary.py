#!/usr/bin/env python3
"""
Excel报告查看工具
"""
import pandas as pd
import sys

def view_excel_summary(excel_file):
    """查看Excel报告的摘要信息"""
    try:
        # 读取所有工作表
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print(f"=== 📊 Excel报告摘要: {excel_file} ===\n")
        
        # 显示工作表信息
        print(f"📋 工作表列表:")
        for sheet_name in excel_data.keys():
            df = excel_data[sheet_name]
            print(f"  - {sheet_name}: {len(df)} 行 × {len(df.columns)} 列")
        
        # 详细分析结果摘要
        if '详细分析结果' in excel_data:
            df_detail = excel_data['详细分析结果']
            print(f"\n📊 详细分析结果摘要:")
            print(f"  总文件数: {len(df_detail)}")
            print(f"  总组数: {df_detail['group_id'].nunique()}")
            print(f"  条件类型: {sorted(df_detail['condition'].unique())}")
            print(f"  布局类型: {sorted(df_detail['layout_type'].unique())}")
            
            # 显示前5行和后5行
            print(f"\n📋 前5行数据:")
            print(df_detail[['group_id', 'filename', 'condition', 'total_efficiency', 'total_score']].head())
            
            print(f"\n📋 后5行数据:")
            print(df_detail[['group_id', 'filename', 'condition', 'total_efficiency', 'total_score']].tail())
        
        # 按条件汇总
        if '按条件汇总' in excel_data:
            df_condition = excel_data['按条件汇总']
            print(f"\n📈 按条件汇总:")
            print(df_condition)
        
        # 按组汇总 (显示前10组)
        if '按组汇总' in excel_data:
            df_group = excel_data['按组汇总']
            print(f"\n📊 按组汇总 (前10组):")
            print(df_group.head(10))
        
        # 按布局汇总
        if '按布局汇总' in excel_data:
            df_layout = excel_data['按布局汇总']
            print(f"\n🏗️ 按布局汇总:")
            print(df_layout)
            
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
    else:
        # 查找最新的Excel文件
        import glob
        excel_files = glob.glob("Experiment2Data_Analysis_*.xlsx")
        if excel_files:
            excel_file = max(excel_files)  # 获取最新的文件
            print(f"🔍 自动找到Excel文件: {excel_file}")
        else:
            print("❌ 未找到Excel文件")
            print("使用方法: python view_excel_summary.py [excel_file]")
            return
    
    view_excel_summary(excel_file)

if __name__ == "__main__":
    main()
