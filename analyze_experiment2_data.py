#!/usr/bin/env python3
"""
Experiment2Data 专用分析工具
分析所有实验数据并生成Excel报告
"""
import os
import glob
import json
import pandas as pd
from datetime import datetime
from analyze_universal import load_data, detect_layout, get_layout_config, analyze_interactions, extract_rewards

def find_experiment_files():
    """发现Experiment2Data中的所有有效文件"""
    base_dir = "Experiment2Data"
    if not os.path.exists(base_dir):
        print(f"❌ 目录 {base_dir} 不存在")
        return {}
    
    # 获取所有子目录
    subdirs = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
    subdirs.sort(key=lambda x: int(x) if x.isdigit() else float('inf'))
    
    groups = {}
    total_files = 0
    
    print(f"🔍 扫描 {base_dir} 目录...")
    
    for subdir in subdirs:
        subdir_path = os.path.join(base_dir, subdir)
        json_files = glob.glob(os.path.join(subdir_path, "*.json"))
        
        # 过滤掉 *lx.json 文件
        valid_files = [f for f in json_files if not os.path.basename(f).endswith('lx.json')]
        
        if valid_files:
            groups[subdir] = {
                'group_id': subdir,
                'files': valid_files,
                'file_count': len(valid_files)
            }
            total_files += len(valid_files)
            
            print(f"  📁 组 {subdir}: {len(valid_files)} 个文件")
            for file in valid_files:
                print(f"    - {os.path.basename(file)}")
    
    print(f"\n📊 总计: {len(groups)} 个组, {total_files} 个文件")
    return groups

def analyze_single_file(filepath):
    """分析单个文件 - 增强版本，包含更详细的统计"""
    try:
        # 加载数据
        data = load_data(filepath)
        if not data:
            return None

        # 提取基本信息
        observations = data.get('ep_observations', [])
        actions = data.get('ep_actions', [])

        if not observations or not observations[0]:
            return None

        # 检测layout
        layout_type = detect_layout(observations, filepath)

        # 分析交互
        interaction_stats = analyze_interactions(observations, actions, layout_type)

        # 提取得分
        rewards = extract_rewards(data)

        # 计算统计数据
        total_valid = interaction_stats['total']['valid']
        total_invalid = interaction_stats['total']['invalid']
        total_interactions = total_valid + total_invalid
        total_efficiency = (total_valid / total_interactions * 100) if total_interactions > 0 else 0

        p0_valid = interaction_stats['player0']['valid']
        p0_invalid = interaction_stats['player0']['invalid']
        p0_total = p0_valid + p0_invalid
        p0_efficiency = (p0_valid / p0_total * 100) if p0_total > 0 else 0

        p1_valid = interaction_stats['player1']['valid']
        p1_invalid = interaction_stats['player1']['invalid']
        p1_total = p1_valid + p1_invalid
        p1_efficiency = (p1_valid / p1_total * 100) if p1_total > 0 else 0

        total_score = sum(rewards) if rewards else 0

        # 增强统计 - 类似analyze_single_detailed.py
        avg_score = total_score / len(rewards) if rewards else 0
        max_score = max(rewards) if rewards else 0
        min_score = min(rewards) if rewards else 0
        interactions_per_score = total_interactions / total_score if total_score > 0 else 0
        valid_per_score = total_valid / total_score if total_score > 0 else 0

        # 效率评级
        if total_efficiency >= 80:
            efficiency_rating = "优秀"
        elif total_efficiency >= 60:
            efficiency_rating = "良好"
        elif total_efficiency >= 40:
            efficiency_rating = "一般"
        else:
            efficiency_rating = "需要改进"
        
        # 提取文件信息
        filename = os.path.basename(filepath)
        group_id = os.path.basename(os.path.dirname(filepath))
        
        # 确定布局和条件类型
        if 'dasy' in filename:
            condition = 'D-ASY'
        elif 'dcc' in filename:
            condition = 'D-CC'
        elif 'gasy' in filename:
            condition = 'G-ASY'
        elif 'gcc' in filename:
            condition = 'G-CC'
        else:
            condition = 'Unknown'
        
        result = {
            'group_id': group_id,
            'filename': filename,
            'filepath': filepath,
            'condition': condition,
            'layout_type': layout_type,
            'episodes': len(observations) if observations else 0,
            'steps_per_episode': len(observations[0]) if observations and observations[0] else 0,
            'total_interactions': total_interactions,
            'total_valid': total_valid,
            'total_invalid': total_invalid,
            'total_efficiency': round(total_efficiency, 2),
            'p0_interactions': p0_total,
            'p0_valid': p0_valid,
            'p0_invalid': p0_invalid,
            'p0_efficiency': round(p0_efficiency, 2),
            'p1_interactions': p1_total,
            'p1_valid': p1_valid,
            'p1_invalid': p1_invalid,
            'p1_efficiency': round(p1_efficiency, 2),
            'total_score': total_score,
            'avg_score': round(avg_score, 2),
            'max_score': max_score,
            'min_score': min_score,
            'interactions_per_score': round(interactions_per_score, 2),
            'valid_per_score': round(valid_per_score, 2),
            'efficiency_rating': efficiency_rating,
            'p0_interaction_ratio': round((p0_total / total_interactions * 100) if total_interactions > 0 else 0, 2),
            'p1_interaction_ratio': round((p1_total / total_interactions * 100) if total_interactions > 0 else 0, 2)
        }
        
        return result
        
    except Exception as e:
        print(f"    ❌ 分析失败: {str(e)}")
        return None

def analyze_all_groups(groups):
    """分析所有组的数据"""
    all_results = []
    
    print(f"\n🚀 开始分析所有数据...")
    
    for group_id, group_info in groups.items():
        print(f"\n📊 分析组 {group_id} ({group_info['file_count']} 个文件):")
        
        group_results = []
        for filepath in group_info['files']:
            filename = os.path.basename(filepath)
            print(f"  正在分析: {filename}")
            
            result = analyze_single_file(filepath)
            if result:
                group_results.append(result)
                all_results.append(result)
                print(f"    ✓ {result['condition']}, 效率: {result['total_efficiency']}%, 得分: {result['total_score']}")
            else:
                print(f"    ❌ 分析失败")
        
        print(f"  组 {group_id} 完成: {len(group_results)}/{group_info['file_count']} 个文件成功")
    
    return all_results

def generate_excel_report(results):
    """生成Excel报告"""
    if not results:
        print("❌ 没有分析结果，无法生成报告")
        return
    
    print(f"\n📊 生成Excel报告...")
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 重新排列列的顺序并创建中英文对照表头
    column_order = [
        'group_id', 'filename', 'condition', 'layout_type',
        'total_interactions', 'total_valid', 'total_invalid', 'total_efficiency',
        'p0_interactions', 'p0_valid', 'p0_invalid', 'p0_efficiency', 'p0_interaction_ratio',
        'p1_interactions', 'p1_valid', 'p1_invalid', 'p1_efficiency', 'p1_interaction_ratio',
        'total_score', 'avg_score', 'max_score', 'min_score',
        'interactions_per_score', 'valid_per_score', 'efficiency_rating',
        'episodes', 'steps_per_episode', 'filepath'
    ]

    # 中英文对照表头
    column_mapping = {
        'group_id': '组号 Group_ID',
        'filename': '文件名 Filename',
        'condition': '实验条件 Condition',
        'layout_type': '布局类型 Layout_Type',
        'total_interactions': '总交互次数 Total_Interactions',
        'total_valid': '有效交互次数 Valid_Interactions',
        'total_invalid': '无效交互次数 Invalid_Interactions',
        'total_efficiency': '总体效率(%) Total_Efficiency',
        'p0_interactions': 'P0交互次数 P0_Interactions',
        'p0_valid': 'P0有效交互 P0_Valid',
        'p0_invalid': 'P0无效交互 P0_Invalid',
        'p0_efficiency': 'P0效率(%) P0_Efficiency',
        'p0_interaction_ratio': 'P0交互占比(%) P0_Ratio',
        'p1_interactions': 'P1交互次数 P1_Interactions',
        'p1_valid': 'P1有效交互 P1_Valid',
        'p1_invalid': 'P1无效交互 P1_Invalid',
        'p1_efficiency': 'P1效率(%) P1_Efficiency',
        'p1_interaction_ratio': 'P1交互占比(%) P1_Ratio',
        'total_score': '总得分 Total_Score',
        'avg_score': '平均得分 Avg_Score',
        'max_score': '最高得分 Max_Score',
        'min_score': '最低得分 Min_Score',
        'interactions_per_score': '每分交互次数 Interactions_Per_Score',
        'valid_per_score': '每分有效交互 Valid_Per_Score',
        'efficiency_rating': '效率评级 Efficiency_Rating',
        'episodes': '游戏轮数 Episodes',
        'steps_per_episode': '每轮步数 Steps_Per_Episode',
        'filepath': '文件路径 Filepath'
    }

    df = df[column_order]
    df = df.rename(columns=column_mapping)
    
    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_filename = f"Experiment2Data_Analysis_{timestamp}.xlsx"
    
    # 创建Excel写入器
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        # 主数据表
        df.to_excel(writer, sheet_name='详细分析结果', index=False)
        
        # 按组汇总
        group_summary = df.groupby('group_id').agg({
            'total_interactions': 'sum',
            'total_valid': 'sum',
            'total_efficiency': 'mean',
            'p0_efficiency': 'mean',
            'p1_efficiency': 'mean',
            'total_score': 'sum',
            'avg_score': 'mean'
        }).round(2)
        group_summary['file_count'] = df.groupby('group_id').size()
        group_summary.to_excel(writer, sheet_name='按组汇总')
        
        # 按条件汇总
        condition_summary = df.groupby('condition').agg({
            'total_interactions': ['sum', 'mean'],
            'total_valid': ['sum', 'mean'],
            'total_efficiency': 'mean',
            'p0_efficiency': 'mean',
            'p1_efficiency': 'mean',
            'total_score': ['sum', 'mean'],
            'avg_score': 'mean'
        }).round(2)
        condition_summary.columns = ['_'.join(col).strip() for col in condition_summary.columns]
        condition_summary['file_count'] = df.groupby('condition').size()
        condition_summary.to_excel(writer, sheet_name='按条件汇总')
        
        # 按布局汇总
        layout_summary = df.groupby('layout_type').agg({
            'total_interactions': ['sum', 'mean'],
            'total_valid': ['sum', 'mean'],
            'total_efficiency': 'mean',
            'p0_efficiency': 'mean',
            'p1_efficiency': 'mean',
            'total_score': ['sum', 'mean'],
            'avg_score': 'mean'
        }).round(2)
        layout_summary.columns = ['_'.join(col).strip() for col in layout_summary.columns]
        layout_summary['file_count'] = df.groupby('layout_type').size()
        layout_summary.to_excel(writer, sheet_name='按布局汇总')
    
    print(f"✅ Excel报告已保存: {excel_filename}")
    
    # 生成简要统计
    print(f"\n📈 分析统计:")
    print(f"  总文件数: {len(results)}")
    print(f"  总组数: {df['group_id'].nunique()}")
    print(f"  条件类型: {sorted(df['condition'].unique())}")
    print(f"  布局类型: {sorted(df['layout_type'].unique())}")
    
    # 按条件统计
    print(f"\n📊 按条件统计:")
    for condition in sorted(df['condition'].unique()):
        condition_data = df[df['condition'] == condition]
        avg_eff = condition_data['total_efficiency'].mean()
        avg_score = condition_data['total_score'].mean()
        print(f"  {condition}: {len(condition_data)} 个文件, 平均效率: {avg_eff:.2f}%, 平均得分: {avg_score:.0f}")
    
    return excel_filename

def main():
    """主函数"""
    print("=== 🧪 Experiment2Data 专用分析工具 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 发现所有文件
    groups = find_experiment_files()
    if not groups:
        print("❌ 未发现任何有效数据文件")
        return
    
    # 分析所有数据
    results = analyze_all_groups(groups)
    
    if not results:
        print("❌ 没有成功分析的文件")
        return
    
    # 生成Excel报告
    excel_file = generate_excel_report(results)
    
    print(f"\n🎉 分析完成！")
    print(f"📁 Excel报告: {excel_file}")
    print(f"📊 成功分析: {len(results)} 个文件")

if __name__ == "__main__":
    main()
