#!/usr/bin/env python3
"""
批量分析所有数据文件并生成汇总报告
"""
import os
import json
import csv
import subprocess
from typing import Dict, List

def run_analysis(filename: str) -> Dict:
    """运行单个文件的分析"""
    try:
        result = subprocess.run(
            ['python', 'analyze_universal.py', filename],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            # 解析输出获取关键信息
            output = result.stdout
            lines = output.split('\n')
            
            # 提取关键数据
            data = {'filename': filename, 'success': True}
            
            for line in lines:
                if '检测到的Layout类型:' in line:
                    data['layout_type'] = line.split(':')[1].strip()
                elif '总有效交互次数:' in line:
                    data['total_valid'] = int(line.split(':')[1].strip())
                elif '总无效交互次数:' in line:
                    data['total_invalid'] = int(line.split(':')[1].strip())
                elif '总交互次数:' in line:
                    data['total_interactions'] = int(line.split(':')[1].strip())
                elif '有效交互比例:' in line and not 'Player' in line:
                    eff_str = line.split(':')[1].strip().replace('%', '')
                    data['total_efficiency'] = float(eff_str)
                elif 'Player0' in line and '有效交互比例:' in line:
                    # 找到下一行包含比例的行
                    idx = lines.index(line)
                    for next_line in lines[idx:idx+5]:
                        if '有效交互比例:' in next_line and 'Player' not in next_line:
                            eff_str = next_line.split(':')[1].strip().replace('%', '')
                            data['p0_efficiency'] = float(eff_str)
                            break
                elif 'Player1' in line and '有效交互比例:' in line:
                    # 找到下一行包含比例的行
                    idx = lines.index(line)
                    for next_line in lines[idx:idx+5]:
                        if '有效交互比例:' in next_line and 'Player' not in next_line:
                            eff_str = next_line.split(':')[1].strip().replace('%', '')
                            data['p1_efficiency'] = float(eff_str)
                            break
                elif '总得分:' in line:
                    data['total_score'] = int(line.split(':')[1].strip())
            
            return data
        else:
            return {'filename': filename, 'success': False, 'error': result.stderr}
    
    except Exception as e:
        return {'filename': filename, 'success': False, 'error': str(e)}

def main():
    """批量分析主函数"""
    print("=== 批量游戏交互分析工具 ===\n")
    
    # 定义要分析的文件
    files = [
        'data/d-asy copy.json',
        'data/d-cc.json',
        'data/g-asy.json', 
        'data/g-cc.json'
    ]
    
    # 存储所有结果
    results = []
    
    # 逐个分析文件
    for filename in files:
        if os.path.exists(filename):
            print(f"正在分析: {filename}")
            result = run_analysis(filename)
            results.append(result)
            
            if result['success']:
                print(f"  ✓ 完成 - Layout: {result.get('layout_type', 'unknown')}, "
                      f"效率: {result.get('total_efficiency', 0):.1f}%, "
                      f"得分: {result.get('total_score', 0)}")
            else:
                print(f"  ✗ 失败 - {result.get('error', 'Unknown error')}")
        else:
            print(f"文件不存在: {filename}")
            results.append({'filename': filename, 'success': False, 'error': 'File not found'})
        print()
    
    # 生成汇总报告
    print("=== 汇总分析报告 ===")
    
    successful_results = [r for r in results if r['success']]
    
    if not successful_results:
        print("没有成功分析的文件")
        return
    
    # 按layout类型分组
    asy_results = [r for r in successful_results if r.get('layout_type') == 'asy']
    cc_results = [r for r in successful_results if r.get('layout_type') == 'cc']
    
    print(f"\n总共分析了 {len(successful_results)} 个文件")
    print(f"ASY布局文件: {len(asy_results)} 个")
    print(f"CC布局文件: {len(cc_results)} 个")
    
    # ASY布局统计
    if asy_results:
        print(f"\n=== ASY布局统计 ===")
        asy_total_interactions = sum(r['total_interactions'] for r in asy_results)
        asy_total_valid = sum(r['total_valid'] for r in asy_results)
        asy_avg_efficiency = sum(r['total_efficiency'] for r in asy_results) / len(asy_results)
        asy_avg_score = sum(r['total_score'] for r in asy_results) / len(asy_results)
        asy_avg_p0_eff = sum(r['p0_efficiency'] for r in asy_results) / len(asy_results)
        asy_avg_p1_eff = sum(r['p1_efficiency'] for r in asy_results) / len(asy_results)
        
        print(f"总交互次数: {asy_total_interactions}")
        print(f"总有效交互: {asy_total_valid}")
        print(f"平均效率: {asy_avg_efficiency:.2f}%")
        print(f"平均得分: {asy_avg_score:.0f}")
        print(f"Human玩家平均效率: {asy_avg_p0_eff:.2f}%")
        print(f"AI玩家平均效率: {asy_avg_p1_eff:.2f}%")
        
        print("详细数据:")
        for r in asy_results:
            print(f"  {r['filename']}: 效率{r['total_efficiency']:.1f}%, 得分{r['total_score']}")
    
    # CC布局统计
    if cc_results:
        print(f"\n=== CC布局统计 ===")
        cc_total_interactions = sum(r['total_interactions'] for r in cc_results)
        cc_total_valid = sum(r['total_valid'] for r in cc_results)
        cc_avg_efficiency = sum(r['total_efficiency'] for r in cc_results) / len(cc_results)
        cc_avg_score = sum(r['total_score'] for r in cc_results) / len(cc_results)
        cc_avg_p0_eff = sum(r['p0_efficiency'] for r in cc_results) / len(cc_results)
        cc_avg_p1_eff = sum(r['p1_efficiency'] for r in cc_results) / len(cc_results)
        
        print(f"总交互次数: {cc_total_interactions}")
        print(f"总有效交互: {cc_total_valid}")
        print(f"平均效率: {cc_avg_efficiency:.2f}%")
        print(f"平均得分: {cc_avg_score:.0f}")
        print(f"Human玩家平均效率: {cc_avg_p0_eff:.2f}%")
        print(f"AI玩家平均效率: {cc_avg_p1_eff:.2f}%")
        
        print("详细数据:")
        for r in cc_results:
            print(f"  {r['filename']}: 效率{r['total_efficiency']:.1f}%, 得分{r['total_score']}")
    
    # 布局对比
    if asy_results and cc_results:
        print(f"\n=== 布局对比分析 ===")
        print(f"{'指标':<20} {'ASY布局':<15} {'CC布局':<15} {'差异':<15}")
        print("-" * 65)
        
        eff_diff = asy_avg_efficiency - cc_avg_efficiency
        score_diff = asy_avg_score - cc_avg_score
        p0_diff = asy_avg_p0_eff - cc_avg_p0_eff
        p1_diff = asy_avg_p1_eff - cc_avg_p1_eff
        
        print(f"{'平均效率(%)':<20} {asy_avg_efficiency:<15.2f} {cc_avg_efficiency:<15.2f} {eff_diff:+.2f}")
        print(f"{'平均得分':<20} {asy_avg_score:<15.0f} {cc_avg_score:<15.0f} {score_diff:+.0f}")
        print(f"{'Human玩家效率(%)':<20} {asy_avg_p0_eff:<15.2f} {cc_avg_p0_eff:<15.2f} {p0_diff:+.2f}")
        print(f"{'AI玩家效率(%)':<20} {asy_avg_p1_eff:<15.2f} {cc_avg_p1_eff:<15.2f} {p1_diff:+.2f}")
    
    # 保存汇总CSV
    print(f"\n=== 保存汇总数据 ===")
    
    # 保存详细结果
    with open('batch_analysis_results.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
        if successful_results:
            fieldnames = ['filename', 'layout_type', 'total_interactions', 'total_valid', 'total_invalid', 
                         'total_efficiency', 'p0_efficiency', 'p1_efficiency', 'total_score']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in successful_results:
                row = {field: result.get(field, '') for field in fieldnames}
                writer.writerow(row)
    
    # 保存汇总统计
    summary_data = []
    if asy_results:
        summary_data.append({
            'layout_type': 'ASY',
            'file_count': len(asy_results),
            'avg_efficiency': asy_avg_efficiency,
            'avg_score': asy_avg_score,
            'avg_p0_efficiency': asy_avg_p0_eff,
            'avg_p1_efficiency': asy_avg_p1_eff
        })
    
    if cc_results:
        summary_data.append({
            'layout_type': 'CC', 
            'file_count': len(cc_results),
            'avg_efficiency': cc_avg_efficiency,
            'avg_score': cc_avg_score,
            'avg_p0_efficiency': cc_avg_p0_eff,
            'avg_p1_efficiency': cc_avg_p1_eff
        })
    
    with open('layout_comparison.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
        if summary_data:
            fieldnames = ['layout_type', 'file_count', 'avg_efficiency', 'avg_score', 
                         'avg_p0_efficiency', 'avg_p1_efficiency']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(summary_data)
    
    print("已保存详细结果到: batch_analysis_results.csv")
    print("已保存汇总对比到: layout_comparison.csv")

if __name__ == "__main__":
    main()
