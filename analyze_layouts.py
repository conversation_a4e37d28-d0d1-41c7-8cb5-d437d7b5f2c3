#!/usr/bin/env python3
"""
分析所有布局类型的特征
"""
import json
import os

def analyze_layout_features(filename):
    """分析单个文件的布局特征"""
    try:
        with open(filename, 'r') as f:
            data = json.load(f)
    except:
        print(f"无法读取文件: {filename}")
        return None
    
    observations = data.get('ep_observations', [[]])[0]
    if not observations:
        return None
    
    # 收集玩家位置
    player_positions = set()
    for step in observations[:50]:  # 分析前50步
        for player in step.get('players', []):
            player_positions.add(tuple(player['position']))
    
    # 收集物品位置
    onion_positions = set()
    soup_positions = set()
    dish_positions = set()
    
    for step in observations:
        # 玩家手中的物品位置
        for player in step.get('players', []):
            held = player.get('held_object')
            if held:
                pos = tuple(held['position'])
                if held['name'] == 'onion':
                    onion_positions.add(pos)
                elif held['name'] == 'dish':
                    dish_positions.add(pos)
        
        # 环境中的物品
        for obj in step.get('objects', []):
            pos = tuple(obj['position'])
            if obj['name'] == 'soup':
                soup_positions.add(pos)
    
    # 推测spawn点
    onion_spawn_candidates = set()
    dish_spawn_candidates = set()
    delivery_candidates = set()
    
    for i, step in enumerate(observations[1:], 1):
        prev_step = observations[i-1]
        
        for j, player in enumerate(step.get('players', [])):
            if j >= len(prev_step.get('players', [])):
                continue
            prev_player = prev_step['players'][j]
            
            # 检测拾取洋葱
            if (not prev_player.get('held_object') and 
                player.get('held_object', {}).get('name') == 'onion'):
                onion_spawn_candidates.add(tuple(player['position']))
            
            # 检测拾取盘子
            if (not prev_player.get('held_object') and 
                player.get('held_object', {}).get('name') == 'dish'):
                dish_spawn_candidates.add(tuple(player['position']))
            
            # 检测送餐
            if (prev_player.get('held_object', {}).get('name') == 'soup' and 
                not player.get('held_object')):
                delivery_candidates.add(tuple(player['position']))
    
    # 计算布局范围
    if player_positions:
        x_coords = [pos[0] for pos in player_positions]
        y_coords = [pos[1] for pos in player_positions]
        layout_bounds = {
            'x_min': min(x_coords), 'x_max': max(x_coords),
            'y_min': min(y_coords), 'y_max': max(y_coords)
        }
    else:
        layout_bounds = {'x_min': 0, 'x_max': 0, 'y_min': 0, 'y_max': 0}
    
    return {
        'filename': filename,
        'player_positions': sorted(player_positions),
        'layout_bounds': layout_bounds,
        'onion_spawn': sorted(onion_spawn_candidates),
        'dish_spawn': sorted(dish_spawn_candidates),
        'pot_positions': sorted(soup_positions),
        'delivery_points': sorted(delivery_candidates),
        'total_positions': len(player_positions)
    }

def main():
    """分析所有数据文件"""
    files = [
        'data/d-asy copy.json',
        'data/d-cc.json', 
        'data/g-asy.json',
        'data/g-cc.json'
    ]
    
    print("=== 布局特征分析 ===\n")
    
    results = {}
    for filename in files:
        if os.path.exists(filename):
            print(f"分析文件: {filename}")
            result = analyze_layout_features(filename)
            if result:
                results[filename] = result
                
                bounds = result['layout_bounds']
                print(f"  布局范围: X({bounds['x_min']}-{bounds['x_max']}), Y({bounds['y_min']}-{bounds['y_max']})")
                print(f"  玩家活动位置数: {result['total_positions']}")
                print(f"  洋葱spawn点: {result['onion_spawn']}")
                print(f"  盘子spawn点: {result['dish_spawn']}")
                print(f"  锅位置: {result['pot_positions']}")
                print(f"  送餐点: {result['delivery_points']}")
                print()
        else:
            print(f"文件不存在: {filename}\n")
    
    # 分析布局类型模式
    print("=== 布局类型分析 ===")
    
    asy_files = [f for f in results.keys() if 'asy' in f]
    cc_files = [f for f in results.keys() if 'cc' in f]
    
    if asy_files:
        print("ASY布局特征:")
        for filename in asy_files:
            r = results[filename]
            bounds = r['layout_bounds']
            print(f"  {filename}: X({bounds['x_min']}-{bounds['x_max']}), Y({bounds['y_min']}-{bounds['y_max']}), 位置数:{r['total_positions']}")
    
    if cc_files:
        print("CC布局特征:")
        for filename in cc_files:
            r = results[filename]
            bounds = r['layout_bounds']
            print(f"  {filename}: X({bounds['x_min']}-{bounds['x_max']}), Y({bounds['y_min']}-{bounds['y_max']}), 位置数:{r['total_positions']}")
    
    # 生成推荐配置
    print("\n=== 推荐配置 ===")
    
    if asy_files:
        # 合并所有ASY文件的特征
        asy_onion_spawn = set()
        asy_dish_spawn = set()
        asy_pot_positions = set()
        asy_delivery_points = set()
        asy_all_positions = set()
        
        for filename in asy_files:
            r = results[filename]
            asy_onion_spawn.update(r['onion_spawn'])
            asy_dish_spawn.update(r['dish_spawn'])
            asy_pot_positions.update(r['pot_positions'])
            asy_delivery_points.update(r['delivery_points'])
            asy_all_positions.update(r['player_positions'])
        
        print("ASY布局配置:")
        print(f"  ITEM_SPAWN_POINTS = {sorted(asy_onion_spawn)}")
        print(f"  DISH_SPAWN_POINTS = {sorted(asy_dish_spawn)}")
        print(f"  POT_POSITIONS = {sorted(asy_pot_positions)}")
        print(f"  DELIVERY_POINTS = {sorted(asy_delivery_points)}")
        print(f"  COUNTER_POSITIONS = {sorted(asy_all_positions)}")
    
    if cc_files:
        # 合并所有CC文件的特征
        cc_onion_spawn = set()
        cc_dish_spawn = set()
        cc_pot_positions = set()
        cc_delivery_points = set()
        cc_all_positions = set()
        
        for filename in cc_files:
            r = results[filename]
            cc_onion_spawn.update(r['onion_spawn'])
            cc_dish_spawn.update(r['dish_spawn'])
            cc_pot_positions.update(r['pot_positions'])
            cc_delivery_points.update(r['delivery_points'])
            cc_all_positions.update(r['player_positions'])
        
        print("CC布局配置:")
        print(f"  ITEM_SPAWN_POINTS = {sorted(cc_onion_spawn)}")
        print(f"  DISH_SPAWN_POINTS = {sorted(cc_dish_spawn)}")
        print(f"  POT_POSITIONS = {sorted(cc_pot_positions)}")
        print(f"  DELIVERY_POINTS = {sorted(cc_delivery_points)}")
        print(f"  COUNTER_POSITIONS = {sorted(cc_all_positions)}")

if __name__ == "__main__":
    main()
