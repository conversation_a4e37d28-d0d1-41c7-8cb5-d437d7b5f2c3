#!/usr/bin/env python3
"""
Experiment2Data 增强版分析工具
结合了analyze_single_detailed.py的详细分析和批量处理能力
包含中英文双语表头和更丰富的统计指标
"""
import os
import glob
import json
import pandas as pd
from datetime import datetime
from analyze_universal import load_data, detect_layout, get_layout_config, analyze_interactions, extract_rewards

def find_experiment_files():
    """发现Experiment2Data中的所有有效文件"""
    base_dir = "Experiment2Data"
    if not os.path.exists(base_dir):
        print(f"❌ 目录 {base_dir} 不存在")
        return {}
    
    subdirs = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
    subdirs.sort(key=lambda x: int(x) if x.isdigit() else float('inf'))
    
    groups = {}
    total_files = 0
    
    print(f"🔍 扫描 {base_dir} 目录...")
    
    for subdir in subdirs:
        subdir_path = os.path.join(base_dir, subdir)
        json_files = glob.glob(os.path.join(subdir_path, "*.json"))
        
        # 过滤掉 *lx.json 文件
        valid_files = [f for f in json_files if not os.path.basename(f).endswith('lx.json')]
        
        if valid_files:
            groups[subdir] = {
                'group_id': subdir,
                'files': valid_files,
                'file_count': len(valid_files)
            }
            total_files += len(valid_files)
            
            print(f"  📁 组 {subdir}: {len(valid_files)} 个文件")
    
    print(f"\n📊 总计: {len(groups)} 个组, {total_files} 个文件")
    return groups

def analyze_single_file_enhanced(filepath):
    """增强版单文件分析 - 结合两种方法的优势"""
    try:
        # 加载数据
        data = load_data(filepath)
        if not data:
            return None
        
        # 提取基本信息
        observations = data.get('ep_observations', [])
        actions = data.get('ep_actions', [])
        
        if not observations or not observations[0]:
            return None
        
        # 检测layout
        layout_type = detect_layout(observations, filepath)
        
        # 分析交互
        interaction_stats = analyze_interactions(observations, actions, layout_type)
        
        # 提取得分
        rewards = extract_rewards(data)
        
        # 基础统计
        total_valid = interaction_stats['total']['valid']
        total_invalid = interaction_stats['total']['invalid']
        total_interactions = total_valid + total_invalid
        total_efficiency = (total_valid / total_interactions * 100) if total_interactions > 0 else 0
        
        p0_valid = interaction_stats['player0']['valid']
        p0_invalid = interaction_stats['player0']['invalid']
        p0_total = p0_valid + p0_invalid
        p0_efficiency = (p0_valid / p0_total * 100) if p0_total > 0 else 0
        
        p1_valid = interaction_stats['player1']['valid']
        p1_invalid = interaction_stats['player1']['invalid']
        p1_total = p1_valid + p1_invalid
        p1_efficiency = (p1_valid / p1_total * 100) if p1_total > 0 else 0
        
        # 得分统计
        total_score = sum(rewards) if rewards else 0
        avg_score = total_score / len(rewards) if rewards else 0
        max_score = max(rewards) if rewards else 0
        min_score = min(rewards) if rewards else 0
        score_std = pd.Series(rewards).std() if rewards and len(rewards) > 1 else 0
        
        # 效率分析
        interactions_per_score = total_interactions / total_score if total_score > 0 else 0
        valid_per_score = total_valid / total_score if total_score > 0 else 0
        
        # 效率评级
        if total_efficiency >= 80:
            efficiency_rating = "优秀 Excellent"
        elif total_efficiency >= 60:
            efficiency_rating = "良好 Good"
        elif total_efficiency >= 40:
            efficiency_rating = "一般 Average"
        else:
            efficiency_rating = "需要改进 Needs Improvement"
        
        # 玩家比例分析
        p0_interaction_ratio = (p0_total / total_interactions * 100) if total_interactions > 0 else 0
        p1_interaction_ratio = (p1_total / total_interactions * 100) if total_interactions > 0 else 0
        
        # 文件信息
        filename = os.path.basename(filepath)
        group_id = os.path.basename(os.path.dirname(filepath))
        
        # 确定实验条件
        if 'dasy' in filename.lower():
            condition = 'D-ASY'
        elif 'dcc' in filename.lower():
            condition = 'D-CC'
        elif 'gasy' in filename.lower():
            condition = 'G-ASY'
        elif 'gcc' in filename.lower():
            condition = 'G-CC'
        else:
            condition = 'Unknown'
        
        # 数据质量指标
        data_quality = "良好 Good"
        if total_interactions == 0:
            data_quality = "无交互 No Interactions"
        elif total_efficiency < 10:
            data_quality = "异常 Abnormal"
        
        result = {
            # 基本信息
            'group_id': group_id,
            'filename': filename,
            'condition': condition,
            'layout_type': layout_type,
            'data_quality': data_quality,
            
            # 游戏基础数据
            'episodes': len(observations) if observations else 0,
            'steps_per_episode': len(observations[0]) if observations and observations[0] else 0,
            
            # 交互统计
            'total_interactions': total_interactions,
            'total_valid': total_valid,
            'total_invalid': total_invalid,
            'total_efficiency': round(total_efficiency, 2),
            'efficiency_rating': efficiency_rating,
            
            # Player0 (Human) 统计
            'p0_interactions': p0_total,
            'p0_valid': p0_valid,
            'p0_invalid': p0_invalid,
            'p0_efficiency': round(p0_efficiency, 2),
            'p0_interaction_ratio': round(p0_interaction_ratio, 2),
            
            # Player1 (AI) 统计
            'p1_interactions': p1_total,
            'p1_valid': p1_valid,
            'p1_invalid': p1_invalid,
            'p1_efficiency': round(p1_efficiency, 2),
            'p1_interaction_ratio': round(p1_interaction_ratio, 2),
            
            # 得分统计
            'total_score': total_score,
            'avg_score': round(avg_score, 2),
            'max_score': max_score,
            'min_score': min_score,
            'score_std': round(score_std, 2),
            
            # 效率分析
            'interactions_per_score': round(interactions_per_score, 2),
            'valid_per_score': round(valid_per_score, 2),
            
            # 其他
            'filepath': filepath
        }
        
        return result
        
    except Exception as e:
        print(f"    ❌ 分析失败: {str(e)}")
        return None

def generate_enhanced_excel_report(results):
    """生成增强版Excel报告"""
    if not results:
        print("❌ 没有分析结果，无法生成报告")
        return
    
    print(f"\n📊 生成增强版Excel报告...")
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 中英文双语列名映射
    column_mapping = {
        'group_id': '组号\nGroup_ID',
        'filename': '文件名\nFilename',
        'condition': '实验条件\nCondition',
        'layout_type': '布局类型\nLayout_Type',
        'data_quality': '数据质量\nData_Quality',
        'episodes': '游戏轮数\nEpisodes',
        'steps_per_episode': '每轮步数\nSteps_Per_Episode',
        'total_interactions': '总交互次数\nTotal_Interactions',
        'total_valid': '有效交互次数\nValid_Interactions',
        'total_invalid': '无效交互次数\nInvalid_Interactions',
        'total_efficiency': '总体效率(%)\nTotal_Efficiency',
        'efficiency_rating': '效率评级\nEfficiency_Rating',
        'p0_interactions': 'P0交互次数\nP0_Interactions',
        'p0_valid': 'P0有效交互\nP0_Valid',
        'p0_invalid': 'P0无效交互\nP0_Invalid',
        'p0_efficiency': 'P0效率(%)\nP0_Efficiency',
        'p0_interaction_ratio': 'P0交互占比(%)\nP0_Ratio',
        'p1_interactions': 'P1交互次数\nP1_Interactions',
        'p1_valid': 'P1有效交互\nP1_Valid',
        'p1_invalid': 'P1无效交互\nP1_Invalid',
        'p1_efficiency': 'P1效率(%)\nP1_Efficiency',
        'p1_interaction_ratio': 'P1交互占比(%)\nP1_Ratio',
        'total_score': '总得分\nTotal_Score',
        'avg_score': '平均得分\nAvg_Score',
        'max_score': '最高得分\nMax_Score',
        'min_score': '最低得分\nMin_Score',
        'score_std': '得分标准差\nScore_Std',
        'interactions_per_score': '每分交互次数\nInteractions_Per_Score',
        'valid_per_score': '每分有效交互\nValid_Per_Score',
        'filepath': '文件路径\nFilepath'
    }
    
    # 重命名列
    df_renamed = df.rename(columns=column_mapping)
    
    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_filename = f"Experiment2Data_Enhanced_Analysis_{timestamp}.xlsx"
    
    # 创建Excel写入器
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        # 主数据表
        df_renamed.to_excel(writer, sheet_name='详细分析结果 Detailed Results', index=False)
        
        # 按组汇总
        group_summary = df.groupby('group_id').agg({
            'total_interactions': 'sum',
            'total_valid': 'sum',
            'total_efficiency': 'mean',
            'p0_efficiency': 'mean',
            'p1_efficiency': 'mean',
            'total_score': 'sum',
            'avg_score': 'mean'
        }).round(2)
        group_summary['file_count'] = df.groupby('group_id').size()
        group_summary.to_excel(writer, sheet_name='按组汇总 Group Summary')
        
        # 按条件汇总
        condition_summary = df.groupby('condition').agg({
            'total_interactions': ['sum', 'mean', 'std'],
            'total_valid': ['sum', 'mean'],
            'total_efficiency': ['mean', 'std'],
            'p0_efficiency': ['mean', 'std'],
            'p1_efficiency': ['mean', 'std'],
            'total_score': ['sum', 'mean', 'std'],
            'avg_score': ['mean', 'std']
        }).round(2)
        condition_summary.columns = ['_'.join(col).strip() for col in condition_summary.columns]
        condition_summary['file_count'] = df.groupby('condition').size()
        condition_summary.to_excel(writer, sheet_name='按条件汇总 Condition Summary')
        
        # 效率评级统计
        rating_summary = df.groupby(['condition', 'efficiency_rating']).size().unstack(fill_value=0)
        rating_summary.to_excel(writer, sheet_name='效率评级统计 Rating Summary')
    
    print(f"✅ 增强版Excel报告已保存: {excel_filename}")
    return excel_filename

def main():
    """主函数"""
    print("=== 🧪 Experiment2Data 增强版分析工具 ===")
    print("结合详细分析和批量处理，包含中英文双语表头")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 发现所有文件
    groups = find_experiment_files()
    if not groups:
        print("❌ 未发现任何有效数据文件")
        return
    
    # 分析所有数据
    all_results = []
    print(f"\n🚀 开始增强版分析...")
    
    for group_id, group_info in groups.items():
        print(f"\n📊 分析组 {group_id} ({group_info['file_count']} 个文件):")
        
        for filepath in group_info['files']:
            filename = os.path.basename(filepath)
            print(f"  正在分析: {filename}")
            
            result = analyze_single_file_enhanced(filepath)
            if result:
                all_results.append(result)
                print(f"    ✓ {result['condition']}, 效率: {result['total_efficiency']}%, 得分: {result['total_score']}, 评级: {result['efficiency_rating']}")
            else:
                print(f"    ❌ 分析失败")
    
    if not all_results:
        print("❌ 没有成功分析的文件")
        return
    
    # 生成Excel报告
    excel_file = generate_enhanced_excel_report(all_results)
    
    # 生成统计摘要
    df = pd.DataFrame(all_results)
    print(f"\n📈 增强版分析统计:")
    print(f"  总文件数: {len(all_results)}")
    print(f"  总组数: {df['group_id'].nunique()}")
    print(f"  条件类型: {sorted(df['condition'].unique())}")
    print(f"  布局类型: {sorted(df['layout_type'].unique())}")
    print(f"  效率评级分布: {dict(df['efficiency_rating'].value_counts())}")
    
    print(f"\n🎉 增强版分析完成！")
    print(f"📁 Excel报告: {excel_file}")
    print(f"📊 成功分析: {len(all_results)} 个文件")
    print(f"🌟 新增功能: 中英文双语表头、效率评级、得分标准差、数据质量评估")

if __name__ == "__main__":
    main()
