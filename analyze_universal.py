#!/usr/bin/env python3
"""
通用游戏交互分析工具
支持多种layout的自动检测和分析
"""
import json
import csv
import sys
from typing import Dict, List, Any, Tuple

def load_data(filename: str) -> Dict[str, Any]:
    """加载JSON数据文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {filename}")
        return {}
    except json.JSONDecodeError:
        print(f"错误：{filename} 不是有效的JSON文件")
        return {}

def detect_layout(observations: List[List[Dict]], filename: str = "") -> str:
    """自动检测layout类型"""
    if not observations or not observations[0]:
        return 'unknown'

    # 首先尝试从文件名检测
    if filename:
        filename_lower = filename.lower()
        if 'asy' in filename_lower:
            return 'asy'
        elif 'cc' in filename_lower:
            return 'cc'

    # 收集更多步骤的数据进行分析
    all_positions = set()
    pot_positions = set()

    for step in observations[0][:50]:  # 检查前50步
        for player in step.get('players', []):
            all_positions.add(tuple(player['position']))

        # 收集锅的位置信息
        for obj in step.get('objects', []):
            if obj['name'] == 'soup':
                pot_positions.add(tuple(obj['position']))

    if not all_positions:
        return 'unknown'

    # 分析布局特征
    max_x = max(pos[0] for pos in all_positions)
    max_y = max(pos[1] for pos in all_positions)
    min_x = min(pos[0] for pos in all_positions)
    min_y = min(pos[1] for pos in all_positions)

    print(f"布局范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
    print(f"检测到的锅位置: {sorted(pot_positions)}")

    # 基于锅位置和布局特征判断layout类型
    if pot_positions:
        # ASY布局特征: 锅在(4,2), (4,3)位置
        if (4, 2) in pot_positions or (4, 3) in pot_positions:
            return 'asy'
        # CC布局特征: 锅在(3,0), (4,0)位置
        elif (3, 0) in pot_positions or (4, 0) in pot_positions:
            return 'cc'

    # 如果锅位置检测失败，使用布局范围作为备选方案
    position_count = len(all_positions)
    if max_x >= 6 and max_y <= 3:
        # 根据位置数量进一步区分
        if position_count <= 8:
            return 'asy'  # ASY布局位置较少
        else:
            return 'cc'   # CC布局位置较多

    return 'unknown'

def get_layout_config(layout_type: str) -> Dict[str, List[Tuple[int, int]]]:
    """获取layout配置"""
    configs = {
        'asy': {
            'ITEM_SPAWN_POINTS': [(1, 1), (1, 2), (5, 2)],
            'DISH_SPAWN_POINTS': [(2, 2), (3, 3), (5, 3)],
            'SERVING_POINTS': [(3, 2), (7, 1)],
            'POT_POSITIONS': [(4, 2), (4, 3)],
            'COUNTER_POSITIONS': [(1, 1), (1, 2), (1, 3), (5, 2), (5, 3), (6, 2), (6, 3)]
        },
        'cc': {
            'ITEM_SPAWN_POINTS': [(1, 1), (2, 1), (3, 3), (4, 3)],
            'DISH_SPAWN_POINTS': [(1, 2)],
            'SERVING_POINTS': [(6, 2)],
            'POT_POSITIONS': [(3, 0), (4, 0)],
            'COUNTER_POSITIONS': [(1, 1), (1, 2), (1, 3), (2, 1), (2, 3), (3, 1), (3, 3), (4, 3), (5, 3), (6, 1), (6, 2), (6, 3)]
        },
        'unknown': {
            'ITEM_SPAWN_POINTS': [],
            'DISH_SPAWN_POINTS': [],
            'SERVING_POINTS': [],
            'POT_POSITIONS': [],
            'COUNTER_POSITIONS': []
        }
    }

    return configs.get(layout_type, configs['unknown'])

def analyze_interactions(observations: List[List[Dict]], actions: List[List] = None, layout_type: str = 'unknown') -> Dict[str, Dict[str, int]]:
    """分析有效交互和无效交互次数"""
    stats = {
        'player0': {'valid': 0, 'invalid': 0},
        'player1': {'valid': 0, 'invalid': 0},
        'total': {'valid': 0, 'invalid': 0}
    }

    if not observations or not actions:
        return stats

    # 获取layout配置
    config = get_layout_config(layout_type)
    ITEM_SPAWN_POINTS = config['ITEM_SPAWN_POINTS']
    DISH_SPAWN_POINTS = config['DISH_SPAWN_POINTS']
    SERVING_POINTS = config['SERVING_POINTS']
    POT_POSITIONS = config['POT_POSITIONS']
    COUNTER_POSITIONS = config['COUNTER_POSITIONS']

    def is_adjacent(pos1, pos2):
        """判断两个位置是否相邻"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1]) == 1

    def is_interact_valid(timestep_idx, player_idx):
        """判断指定时间步和玩家的INTERACT是否有效"""
        if timestep_idx >= len(observations[0]):
            return False

        obs = observations[0][timestep_idx]
        if player_idx >= len(obs.get("players", [])):
            return False

        player = obs["players"][player_idx]
        pos = tuple(player["position"])
        held = player.get("held_object")

        # 1. 检查现有汤锅交互
        for obj in obs.get("objects", []):
            if obj["name"] == "soup" and is_adjacent(pos, tuple(obj["position"])):
                soup_state = obj["state"]
                if held and held["name"] == "onion":
                    return soup_state[1] < 3  # 锅未满
                elif held and held["name"] == "dish":
                    return soup_state[2] == 20  # 汤已完成
                return False

        # 2. 检查空锅位置交互
        if held and held["name"] == "onion":
            for pot_pos in POT_POSITIONS:
                if is_adjacent(pos, pot_pos):
                    pot_occupied = any(obj["name"] == "soup" and tuple(obj["position"]) == pot_pos 
                                     for obj in obs.get("objects", []))
                    if not pot_occupied:
                        return True

        # 3. 检查物品拾取
        if not held:
            if pos in ITEM_SPAWN_POINTS or pos in DISH_SPAWN_POINTS:
                return True

        # 4. 检查订单提交
        if held and held["name"] == "soup" and pos in SERVING_POINTS:
            return True

        # 5. 检查放下物品
        if held and pos in COUNTER_POSITIONS:
            occupied = any(tuple(obj["position"]) == pos for obj in obs.get("objects", []))
            return not occupied

        return False

    # 统计交互
    if len(actions) > 0 and len(actions[0]) > 0:
        episode_actions = actions[0]
        for timestep_idx, timestep_actions in enumerate(episode_actions):
            for player_idx, action in enumerate(timestep_actions):
                if action == "INTERACT":
                    player_key = f'player{player_idx}'
                    if is_interact_valid(timestep_idx, player_idx):
                        stats[player_key]['valid'] += 1
                        stats['total']['valid'] += 1
                    else:
                        stats[player_key]['invalid'] += 1
                        stats['total']['invalid'] += 1

    return stats

def extract_rewards(data: Dict[str, Any]) -> List[float]:
    """提取每轮得分"""
    rewards = []
    if 'ep_rewards' in data:
        for episode_rewards in data['ep_rewards']:
            if isinstance(episode_rewards, list):
                rewards.append(sum(episode_rewards))
            else:
                rewards.append(episode_rewards)
    return rewards

def main(filename: str):
    """主函数"""
    print("=== 通用游戏交互分析工具 ===")
    print(f"分析文件: {filename}\n")
    
    # 加载数据
    data = load_data(filename)
    if not data:
        return
    
    # 提取数据
    observations = data.get('ep_observations', [])
    actions = data.get('ep_actions', [])
    
    # 检测layout
    layout_type = detect_layout(observations, filename)
    config = get_layout_config(layout_type)
    
    print(f"=== Layout检测结果 ===")
    print(f"检测到的Layout类型: {layout_type}")
    if layout_type != 'unknown':
        print(f"洋葱spawn点: {config['ITEM_SPAWN_POINTS']}")
        print(f"盘子spawn点: {config['DISH_SPAWN_POINTS']}")
        print(f"锅位置: {config['POT_POSITIONS']}")
        print(f"送餐点: {config['SERVING_POINTS']}")
    else:
        print("警告：未能识别layout类型，分析结果可能不准确")
    print()
    
    # 分析交互
    interaction_stats = analyze_interactions(observations, actions, layout_type)
    
    # 提取得分
    rewards = extract_rewards(data)
    
    # 输出结果
    print("=== 分析结果 ===")
    total_valid = interaction_stats['total']['valid']
    total_invalid = interaction_stats['total']['invalid']
    total_interactions = total_valid + total_invalid
    
    print(f"总有效交互次数: {total_valid}")
    print(f"总无效交互次数: {total_invalid}")
    print(f"总交互次数: {total_interactions}")
    print(f"有效交互比例: {total_valid / total_interactions * 100:.2f}%" if total_interactions > 0 else "无交互数据")
    
    # 分别显示两个玩家的统计
    for player_idx in [0, 1]:
        player_name = "Human Keyboard Input" if player_idx == 0 else "Human-aware PPO agent"
        p_valid = interaction_stats[f'player{player_idx}']['valid']
        p_invalid = interaction_stats[f'player{player_idx}']['invalid']
        p_total = p_valid + p_invalid
        
        print(f"\nPlayer{player_idx} ({player_name}) 统计:")
        print(f"  有效交互次数: {p_valid}")
        print(f"  无效交互次数: {p_invalid}")
        print(f"  总交互次数: {p_total}")
        print(f"  有效交互比例: {p_valid / p_total * 100:.2f}%" if p_total > 0 else "无交互数据")
    
    if rewards:
        print(f"\n得分统计:")
        print(f"  轮数: {len(rewards)}")
        print(f"  总得分: {sum(rewards)}")
        print(f"  平均得分: {sum(rewards) / len(rewards):.2f}")
        print(f"  得分列表: {rewards}")
    
    # 保存CSV文件
    output_filename = f"spss_data_{layout_type}.csv"
    spss_data = {
        '文件名': [filename],
        'Layout类型': [layout_type],
        '总有效交互次数': [total_valid],
        '总无效交互次数': [total_invalid],
        '总交互次数': [total_interactions],
        '总有效交互比例': [total_valid / total_interactions * 100 if total_interactions > 0 else 0],
        'P0有效交互次数': [interaction_stats['player0']['valid']],
        'P0无效交互次数': [interaction_stats['player0']['invalid']],
        'P0有效交互比例': [interaction_stats['player0']['valid'] / (interaction_stats['player0']['valid'] + interaction_stats['player0']['invalid']) * 100 if (interaction_stats['player0']['valid'] + interaction_stats['player0']['invalid']) > 0 else 0],
        'P1有效交互次数': [interaction_stats['player1']['valid']],
        'P1无效交互次数': [interaction_stats['player1']['invalid']],
        'P1有效交互比例': [interaction_stats['player1']['valid'] / (interaction_stats['player1']['valid'] + interaction_stats['player1']['invalid']) * 100 if (interaction_stats['player1']['valid'] + interaction_stats['player1']['invalid']) > 0 else 0]
    }
    
    if rewards:
        spss_data.update({
            '轮数': [len(rewards)],
            '总得分': [sum(rewards)],
            '平均得分': [sum(rewards) / len(rewards)]
        })
    
    with open(output_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = list(spss_data.keys())
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        row_data = {key: value[0] for key, value in spss_data.items()}
        writer.writerow(row_data)
    
    print(f"\n已保存分析结果到 {output_filename}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = input("请输入要分析的JSON文件名: ")
    
    main(filename)
