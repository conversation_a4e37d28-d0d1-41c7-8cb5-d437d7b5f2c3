#!/usr/bin/env python3
"""
简化的批量分析工具
"""
import os
import json
import csv
from analyze_universal import main as analyze_main, load_data, detect_layout, get_layout_config, analyze_interactions, extract_rewards

def analyze_file(filename: str) -> dict:
    """分析单个文件"""
    print(f"分析文件: {filename}")
    
    # 加载数据
    data = load_data(filename)
    if not data:
        return {'filename': filename, 'success': False, 'error': 'Failed to load data'}
    
    try:
        # 提取数据
        observations = data.get('ep_observations', [])
        actions = data.get('ep_actions', [])
        
        # 检测layout
        layout_type = detect_layout(observations, filename)
        config = get_layout_config(layout_type)
        
        # 分析交互
        interaction_stats = analyze_interactions(observations, actions, layout_type)
        
        # 提取得分
        rewards = extract_rewards(data)
        
        # 计算统计数据
        total_valid = interaction_stats['total']['valid']
        total_invalid = interaction_stats['total']['invalid']
        total_interactions = total_valid + total_invalid
        total_efficiency = (total_valid / total_interactions * 100) if total_interactions > 0 else 0
        
        p0_valid = interaction_stats['player0']['valid']
        p0_invalid = interaction_stats['player0']['invalid']
        p0_total = p0_valid + p0_invalid
        p0_efficiency = (p0_valid / p0_total * 100) if p0_total > 0 else 0
        
        p1_valid = interaction_stats['player1']['valid']
        p1_invalid = interaction_stats['player1']['invalid']
        p1_total = p1_valid + p1_invalid
        p1_efficiency = (p1_valid / p1_total * 100) if p1_total > 0 else 0
        
        total_score = sum(rewards) if rewards else 0
        
        result = {
            'filename': filename,
            'success': True,
            'layout_type': layout_type,
            'total_valid': total_valid,
            'total_invalid': total_invalid,
            'total_interactions': total_interactions,
            'total_efficiency': total_efficiency,
            'p0_valid': p0_valid,
            'p0_invalid': p0_invalid,
            'p0_total': p0_total,
            'p0_efficiency': p0_efficiency,
            'p1_valid': p1_valid,
            'p1_invalid': p1_invalid,
            'p1_total': p1_total,
            'p1_efficiency': p1_efficiency,
            'total_score': total_score
        }
        
        print(f"  ✓ Layout: {layout_type}, 效率: {total_efficiency:.1f}%, 得分: {total_score}")
        return result
        
    except Exception as e:
        print(f"  ✗ 分析失败: {str(e)}")
        return {'filename': filename, 'success': False, 'error': str(e)}

def main():
    """主函数"""
    print("=== 简化批量游戏交互分析工具 ===\n")
    
    # 定义要分析的文件
    files = [
        'data/d-asy copy.json',
        'data/d-cc.json',
        'data/g-asy.json',
        'data/g-cc.json'
    ]
    
    # 存储所有结果
    results = []
    
    # 逐个分析文件
    for filename in files:
        if os.path.exists(filename):
            result = analyze_file(filename)
            results.append(result)
        else:
            print(f"文件不存在: {filename}")
            results.append({'filename': filename, 'success': False, 'error': 'File not found'})
        print()
    
    # 生成汇总报告
    print("=== 汇总分析报告 ===")
    
    successful_results = [r for r in results if r['success']]
    
    if not successful_results:
        print("没有成功分析的文件")
        return
    
    # 按layout类型分组
    asy_results = [r for r in successful_results if r.get('layout_type') == 'asy']
    cc_results = [r for r in successful_results if r.get('layout_type') == 'cc']
    
    print(f"总共分析了 {len(successful_results)} 个文件")
    print(f"ASY布局文件: {len(asy_results)} 个")
    print(f"CC布局文件: {len(cc_results)} 个")
    
    # ASY布局统计
    if asy_results:
        print(f"\n=== ASY布局统计 ===")
        asy_total_interactions = sum(r['total_interactions'] for r in asy_results)
        asy_total_valid = sum(r['total_valid'] for r in asy_results)
        asy_avg_efficiency = sum(r['total_efficiency'] for r in asy_results) / len(asy_results)
        asy_avg_score = sum(r['total_score'] for r in asy_results) / len(asy_results)
        asy_avg_p0_eff = sum(r['p0_efficiency'] for r in asy_results) / len(asy_results)
        asy_avg_p1_eff = sum(r['p1_efficiency'] for r in asy_results) / len(asy_results)
        
        print(f"总交互次数: {asy_total_interactions}")
        print(f"总有效交互: {asy_total_valid}")
        print(f"平均效率: {asy_avg_efficiency:.2f}%")
        print(f"平均得分: {asy_avg_score:.0f}")
        print(f"Human玩家平均效率: {asy_avg_p0_eff:.2f}%")
        print(f"AI玩家平均效率: {asy_avg_p1_eff:.2f}%")
        
        print("详细数据:")
        for r in asy_results:
            print(f"  {r['filename']}: 效率{r['total_efficiency']:.1f}%, 得分{r['total_score']}")
    
    # CC布局统计
    if cc_results:
        print(f"\n=== CC布局统计 ===")
        cc_total_interactions = sum(r['total_interactions'] for r in cc_results)
        cc_total_valid = sum(r['total_valid'] for r in cc_results)
        cc_avg_efficiency = sum(r['total_efficiency'] for r in cc_results) / len(cc_results)
        cc_avg_score = sum(r['total_score'] for r in cc_results) / len(cc_results)
        cc_avg_p0_eff = sum(r['p0_efficiency'] for r in cc_results) / len(cc_results)
        cc_avg_p1_eff = sum(r['p1_efficiency'] for r in cc_results) / len(cc_results)
        
        print(f"总交互次数: {cc_total_interactions}")
        print(f"总有效交互: {cc_total_valid}")
        print(f"平均效率: {cc_avg_efficiency:.2f}%")
        print(f"平均得分: {cc_avg_score:.0f}")
        print(f"Human玩家平均效率: {cc_avg_p0_eff:.2f}%")
        print(f"AI玩家平均效率: {cc_avg_p1_eff:.2f}%")
        
        print("详细数据:")
        for r in cc_results:
            print(f"  {r['filename']}: 效率{r['total_efficiency']:.1f}%, 得分{r['total_score']}")
    
    # 布局对比
    if asy_results and cc_results:
        print(f"\n=== 布局对比分析 ===")
        print(f"{'指标':<20} {'ASY布局':<15} {'CC布局':<15} {'差异':<15}")
        print("-" * 65)
        
        eff_diff = asy_avg_efficiency - cc_avg_efficiency
        score_diff = asy_avg_score - cc_avg_score
        p0_diff = asy_avg_p0_eff - cc_avg_p0_eff
        p1_diff = asy_avg_p1_eff - cc_avg_p1_eff
        
        print(f"{'平均效率(%)':<20} {asy_avg_efficiency:<15.2f} {cc_avg_efficiency:<15.2f} {eff_diff:+.2f}")
        print(f"{'平均得分':<20} {asy_avg_score:<15.0f} {cc_avg_score:<15.0f} {score_diff:+.0f}")
        print(f"{'Human玩家效率(%)':<20} {asy_avg_p0_eff:<15.2f} {cc_avg_p0_eff:<15.2f} {p0_diff:+.2f}")
        print(f"{'AI玩家效率(%)':<20} {asy_avg_p1_eff:<15.2f} {cc_avg_p1_eff:<15.2f} {p1_diff:+.2f}")
        
        # 分析结论
        print(f"\n=== 关键发现 ===")
        if eff_diff > 0:
            print(f"• ASY布局的交互效率比CC布局高 {eff_diff:.1f}%")
        else:
            print(f"• CC布局的交互效率比ASY布局高 {abs(eff_diff):.1f}%")
        
        if score_diff > 0:
            print(f"• ASY布局的平均得分比CC布局高 {score_diff:.0f}分")
        else:
            print(f"• CC布局的平均得分比ASY布局高 {abs(score_diff):.0f}分")
        
        if p0_diff > 0:
            print(f"• 在ASY布局中，Human玩家效率比CC布局高 {p0_diff:.1f}%")
        else:
            print(f"• 在CC布局中，Human玩家效率比ASY布局高 {abs(p0_diff):.1f}%")
        
        if p1_diff > 0:
            print(f"• 在ASY布局中，AI玩家效率比CC布局高 {p1_diff:.1f}%")
        else:
            print(f"• 在CC布局中，AI玩家效率比ASY布局高 {abs(p1_diff):.1f}%")
    
    # 保存详细结果
    print(f"\n=== 保存数据 ===")
    with open('detailed_analysis_results.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
        if successful_results:
            fieldnames = ['filename', 'layout_type', 'total_interactions', 'total_valid', 'total_invalid', 
                         'total_efficiency', 'p0_efficiency', 'p1_efficiency', 'total_score']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in successful_results:
                row = {field: result.get(field, '') for field in fieldnames}
                writer.writerow(row)
    
    # 保存汇总统计
    summary_data = []
    if asy_results:
        summary_data.append({
            'layout_type': 'ASY',
            'file_count': len(asy_results),
            'avg_efficiency': round(asy_avg_efficiency, 2),
            'avg_score': round(asy_avg_score, 0),
            'avg_p0_efficiency': round(asy_avg_p0_eff, 2),
            'avg_p1_efficiency': round(asy_avg_p1_eff, 2)
        })
    
    if cc_results:
        summary_data.append({
            'layout_type': 'CC',
            'file_count': len(cc_results),
            'avg_efficiency': round(cc_avg_efficiency, 2),
            'avg_score': round(cc_avg_score, 0),
            'avg_p0_efficiency': round(cc_avg_p0_eff, 2),
            'avg_p1_efficiency': round(cc_avg_p1_eff, 2)
        })
    
    with open('layout_summary.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
        if summary_data:
            fieldnames = ['layout_type', 'file_count', 'avg_efficiency', 'avg_score', 
                         'avg_p0_efficiency', 'avg_p1_efficiency']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(summary_data)
    
    print("已保存详细结果到: detailed_analysis_results.csv")
    print("已保存汇总对比到: layout_summary.csv")

if __name__ == "__main__":
    main()
