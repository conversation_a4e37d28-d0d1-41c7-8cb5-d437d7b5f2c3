{"ep_observations": [[{"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 14]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 15]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 16]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 17]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 18]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 19]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0]}, {"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0]}, {"position": [2, 2], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [-1, 0]}, {"position": [3, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [-1, 0]}, {"position": [3, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [3, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 14]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 15]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 16]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 17]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 18]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 19]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}, {"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}, {"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [3, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 2], "state": ["onion", 3, 20]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [3, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 2], "state": ["onion", 3, 20]}}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 2], "state": ["onion", 3, 20]}}, {"position": [2, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 2], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 2], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [2, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [2, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 13]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 14]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 15]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 16]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 17]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 18]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [2, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [2, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 19]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [2, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [2, 2]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [2, 2]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [2, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 2], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [-1, 0]}, {"position": [3, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [3, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [3, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 2], "orientation": [0, 1]}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 2]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 2]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "dish", "position": [2, 3]}, {"name": "soup", "position": [2, 0], "state": ["onion", 2, 0]}], "order_list": null}]], "ep_rewards": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "ep_actions": [[[[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[0, 0], "INTERACT"], [[1, 0], [1, 0]], [[-1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], ["INTERACT", "INTERACT"], [[0, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, -1], [0, -1]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[1, 0], [0, -1]], [[0, 0], [1, 0]], [[0, 0], [0, 0]], [[0, -1], [0, -1]], ["INTERACT", "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[-1, 0], [1, 0]], [[0, 0], [0, -1]], [[0, -1], [-1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], ["INTERACT", [0, -1]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 1], [1, 0]], [[0, 0], [0, -1]], [[0, 1], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], [[-1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, -1], [0, -1]], [[0, 0], [0, -1]], [[-1, 0], [0, -1]], [[0, 0], "INTERACT"], ["INTERACT", [1, 0]], [[0, 0], [-1, 0]], [[1, 0], [0, -1]], [[0, -1], "INTERACT"], [[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, -1], [0, -1]], [[0, 0], [-1, 0]], ["INTERACT", "INTERACT"], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], [-1, 0]], [[0, 0], "INTERACT"], [[0, 1], [0, -1]], ["INTERACT", [0, 0]], [[0, 0], [0, -1]], [[1, 0], [0, 1]], [[0, 0], [0, 0]], [[0, 0], [0, 0]], [[0, -1], [0, 0]], [[0, -1], [0, 0]], [[0, 0], [0, 0]], [[0, 0], [0, -1]], [[0, 0], [0, 0]], [[0, 0], [0, 0]], ["INTERACT", [0, 0]], [[0, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 1], [0, 0]], [[0, 1], "INTERACT"], [[0, 0], [0, 1]], ["INTERACT", [0, -1]], [[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[0, -1], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[1, 0], "INTERACT"], ["INTERACT", [1, 0]], [[0, 0], [0, -1]], [[-1, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[-1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, -1], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], [1, 0]], [[0, -1], [0, 0]], ["INTERACT", "INTERACT"], [[0, 0], [0, -1]], [[0, 1], [0, -1]], [[0, 1], [0, 0]], [[0, 0], [0, 0]], ["INTERACT", [0, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 0]], [[0, 0], [0, -1]], ["INTERACT", [0, 0]], [[0, 0], [0, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[1, 0], [0, 0]], [[0, 0], [0, 0]], [[0, 1], [0, 0]], ["INTERACT", [0, -1]], [[0, 0], "INTERACT"], [[0, -1], [0, -1]], [[0, 0], [0, 1]], [[0, -1], [0, -1]], [[0, 0], [0, -1]], [[-1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, -1], [0, -1]], [[0, 0], [0, 1]], [[0, -1], [0, 1]], ["INTERACT", "INTERACT"], [[0, 0], [0, -1]], [[1, 0], [1, 0]], [[0, 0], "INTERACT"], [[1, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, -1], [-1, 0]], [[0, 0], "INTERACT"], ["INTERACT", [0, -1]], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[-1, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[-1, 0], [0, -1]], ["INTERACT", [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[1, 0], [0, -1]], [[0, 0], [0, -1]], [[1, 0], [0, -1]], [[0, 0], [0, -1]], [[1, 0], "INTERACT"], [[0, 0], [1, 0]], [[0, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[1, 0], [0, -1]]]], "mdp_params": [{"layout_name": "cramped_room", "num_items_for_soup": 3, "rew_shaping_params": null, "cook_time": 20, "start_order_list": null}]}