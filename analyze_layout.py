#!/usr/bin/env python3
import json

with open('test2.json', 'r') as f:
    data = json.load(f)

# 分析游戏布局
obs = data['ep_observations'][0]  # 第一轮游戏
print('分析游戏布局...')

# 找到所有出现过的物品位置
all_positions = set()
onion_positions = set()
soup_positions = set()
dish_positions = set()

for step in obs:  # 看所有步骤
    for obj in step.get('objects', []):
        pos = tuple(obj['position'])
        all_positions.add(pos)
        if obj['name'] == 'onion':
            onion_positions.add(pos)
        elif obj['name'] == 'soup':
            soup_positions.add(pos)
        elif obj['name'] == 'dish':
            dish_positions.add(pos)

print('洋葱出现位置:', sorted(onion_positions))
print('汤锅位置:', sorted(soup_positions))
print('盘子出现位置:', sorted(dish_positions))
print('所有物品位置:', sorted(all_positions))

# 分析玩家位置范围
player_positions = set()
for step in obs:
    for player in step.get('players', []):
        pos = tuple(player['position'])
        player_positions.add(pos)

print('玩家活动范围:', sorted(player_positions))

# 分析动作数据
actions = data['ep_actions'][0]
interact_count = 0
for timestep_actions in actions:
    for action in timestep_actions:
        if action == "INTERACT":
            interact_count += 1

print(f'总INTERACT动作数: {interact_count}')
