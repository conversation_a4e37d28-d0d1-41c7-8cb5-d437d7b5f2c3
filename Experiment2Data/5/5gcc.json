{"ep_observations": [[{"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 3], "orientation": [0, -1]}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [1, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 2], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [1, 2], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 1]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 2]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 3]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 4]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 5]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 6]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 7]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 8]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 9]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 10]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 11]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 12]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 13]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 14]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 15]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 16]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 17]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 18]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 19]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [4, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [4, 3], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 3], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 1], "orientation": [0, 1], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [6, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [6, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [4, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [4, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [6, 2], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [4, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [4, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 14]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [2, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 15]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 16]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 17]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 18]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 19]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [6, 2], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 2], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 2], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [6, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [6, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0]}, {"position": [5, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [5, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [5, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [6, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1]}, {"position": [5, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [6, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [6, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [5, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0]}, {"position": [5, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0]}, {"position": [5, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0]}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0]}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0]}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0]}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [4, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 2], "orientation": [1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [6, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [6, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [5, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 7]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 8]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 9]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 10]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 11]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 12]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 13]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 14]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 15]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 16]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 17]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 18]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 19]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [0, 1]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [6, 2], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [6, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [5, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 1]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 2]}, {"name": "soup", "position": [3, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 3]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 4]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 5]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 6]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 7]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 8]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 9]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 10]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 11]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 12]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 13]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 14]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 15]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 16]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 17]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 18]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 19]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [5, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [5, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [6, 3], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [5, 3], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [5, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 3], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [4, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 3], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [4, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [4, 3], "orientation": [0, -1], "held_object": {"name": "soup", "position": [4, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "soup", "position": [4, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [5, 3], "orientation": [0, -1], "held_object": {"name": "soup", "position": [5, 3], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [5, 3], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [6, 3], "orientation": [1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0]}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [0, -1]}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "onion", "position": [1, 0]}, {"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [2, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}, {"name": "onion", "position": [1, 0]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [2, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}, {"name": "onion", "position": [2, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}, {"name": "onion", "position": [2, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [2, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 2, 0]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 1]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 2]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 3]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 4]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 5]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 6]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 7]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 8]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 9]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 10]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 11]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 12]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 13]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 14]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 15]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 16]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 17]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 18]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 19]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [4, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [4, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [3, 2]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 1]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 2]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 3]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 4]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 5]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [2, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 6]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 7]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [5, 2], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 8]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}, {"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 9]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}, {"position": [2, 1], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 10]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}, {"position": [1, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 11]}, {"name": "onion", "position": [2, 4]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}, {"position": [1, 1], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 12]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 13]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 14]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 15]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 16]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 17]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 18]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 19]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [3, 3], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [2, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 2], "orientation": [0, 1], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [3, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 3]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [4, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [5, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [5, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [6, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 1]}}, {"position": [6, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [6, 1], "orientation": [1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [6, 2], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [6, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [5, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [5, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [5, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [4, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [4, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [2, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [3, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [3, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [2, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [2, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [7, 1], "state": ["onion", 3, 20]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 1], "state": ["onion", 3, 20]}}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [1, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [1, 3], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [1, 3]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0], "held_object": {"name": "soup", "position": [6, 2], "state": ["onion", 3, 20]}}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [1, 0]}, {"position": [1, 3], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [0, 1]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 3], "orientation": [0, 1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [-1, 0]}, {"position": [1, 2], "orientation": [0, -1]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0]}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 2], "orientation": [-1, 0], "held_object": {"name": "dish", "position": [1, 2]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [1, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [1, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1]}, {"position": [2, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [2, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 3], "orientation": [0, 1], "held_object": {"name": "onion", "position": [4, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [5, 3]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 3], "orientation": [1, 0], "held_object": {"name": "onion", "position": [6, 3]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "dish", "position": [3, 1]}}], "objects": [{"name": "soup", "position": [3, 0], "state": ["onion", 3, 20]}, {"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 2]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [4, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [6, 1]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [4, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [5, 1]}}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, -1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [-1, 0], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1], "held_object": {"name": "onion", "position": [4, 1]}}, {"position": [3, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 1, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [4, 1], "orientation": [0, -1]}, {"position": [3, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [5, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [0, 1], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 1], "orientation": [1, 0]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [3, 1], "orientation": [-1, 0], "held_object": {"name": "soup", "position": [3, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}, {"players": [{"position": [6, 2], "orientation": [0, 1]}, {"position": [4, 1], "orientation": [1, 0], "held_object": {"name": "soup", "position": [4, 1], "state": ["onion", 3, 20]}}], "objects": [{"name": "onion", "position": [2, 4]}, {"name": "onion", "position": [0, 1]}, {"name": "soup", "position": [4, 0], "state": ["onion", 2, 0]}, {"name": "onion", "position": [0, 3]}], "order_list": null}]], "ep_rewards": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "ep_actions": [[[[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[-1, 0], [0, 1]], [[0, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 1], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], ["INTERACT", [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[0, 0], [0, 1]], [[1, 0], [1, 0]], [[1, 0], [1, 0]], [[1, 0], [0, 1]], [[0, 0], [0, 1]], [[0, -1], "INTERACT"], [[0, 0], "INTERACT"], [[0, -1], "INTERACT"], [[0, -1], [-1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [0, -1]], [[0, 0], [0, -1]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, -1], [0, -1]], [[0, 0], "INTERACT"], ["INTERACT", [1, 0]], [[0, 0], [0, 1]], [[1, 0], [0, -1]], [[0, 0], [-1, 0]], [[1, 0], [-1, 0]], [[1, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, 0], "INTERACT"], [[0, 1], "INTERACT"], [[0, 1], "INTERACT"], [[0, 1], [-1, 0]], [[0, 1], [0, -1]], [[0, 0], [-1, 0]], [[-1, 0], [0, -1]], [[-1, 0], [1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 1], [0, -1]], ["INTERACT", "INTERACT"], [[1, 0], [1, 0]], [[0, 0], [1, 0]], [[1, 0], [0, -1]], [[1, 0], [0, -1]], [[1, 0], [0, 0]], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], [1, 0]], [[-1, 0], "INTERACT"], [[-1, 0], [0, 1]], [[-1, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[-1, 0], [0, 1]], [[0, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, -1], [-1, 0]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[0, -1], [0, -1]], [[0, -1], [0, -1]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[1, 0], [1, 0]], [[1, 0], [0, -1]], [[0, 0], [0, -1]], ["INTERACT", [0, -1]], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[0, 0], [0, -1]], [[0, 0], [1, 0]], [[0, -1], [0, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], ["INTERACT", [0, -1]], [[1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[1, 0], [-1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[0, 0], [1, 0]], [[0, 1], [0, 1]], [[0, 1], "INTERACT"], [[0, 1], [1, 0]], [[0, 0], [0, 1]], [[0, 1], [0, 0]], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [0, 1]], [[-1, 0], [0, 1]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[0, 1], [0, 1]], [[0, 0], [0, 1]], ["INTERACT", [0, 1]], [[1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[1, 0], [0, 1]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[0, -1], [-1, 0]], [[0, 0], [0, -1]], [[0, -1], [0, 0]], [[0, -1], [1, 0]], [[-1, 0], [0, -1]], [[0, 0], "INTERACT"], [[-1, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, 0]], [[0, -1], [0, -1]], ["INTERACT", [0, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[1, 0], [0, -1]], [[1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 1], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], [0, 1]], [[0, 1], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, 0]], [[-1, 0], "INTERACT"], [[-1, 0], [0, -1]], [[-1, 0], [0, -1]], [[0, 0], [0, 1]], [[0, 1], [0, -1]], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[0, 0], [1, 0]], [[-1, 0], [0, -1]], [[0, 0], "INTERACT"], [[-1, 0], [1, 0]], [[-1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[0, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, -1], [-1, 0]], [[0, -1], [0, 1]], [[0, -1], [-1, 0]], [[0, 0], [0, -1]], [[0, -1], "INTERACT"], [[1, 0], [1, 0]], [[1, 0], [-1, 0]], [[0, 0], [0, 1]], [[0, -1], [-1, 0]], [[0, 0], "INTERACT"], [[0, 0], [0, -1]], ["INTERACT", [1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[-1, 0], [0, 1]], [[0, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], ["INTERACT", [0, -1]], [[0, 0], [0, -1]], [[1, 0], [-1, 0]], [[1, 0], [0, -1]], [[0, 0], [1, 0]], [[0, 0], [-1, 0]], [[0, -1], [0, -1]], [[0, 0], [0, 0]], [[0, 0], [0, 0]], ["INTERACT", [0, 0]], [[1, 0], [0, 0]], [[0, 0], "INTERACT"], [[1, 0], [-1, 0]], [[1, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 1], "INTERACT"], [[0, 1], [0, -1]], [[0, 1], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 1], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[0, 0], [1, 0]], [[-1, 0], [0, 1]], [[-1, 0], [1, 0]], [[-1, 0], "INTERACT"], [[0, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, -1], [-1, 0]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[0, -1], [0, 1]], [[0, -1], "INTERACT"], [[0, 0], [-1, 0]], [[1, 0], [-1, 0]], [[1, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[0, 0], [0, -1]], ["INTERACT", [0, -1]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[1, 0], [0, -1]], [[0, 0], "INTERACT"], [[1, 0], [-1, 0]], [[1, 0], [-1, 0]], [[0, 0], [0, 1]], [[0, 1], [0, 1]], [[0, 1], [1, 0]], [[0, 1], [1, 0]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [0, -1]], [[0, 0], [0, -1]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, -1]], ["INTERACT", "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], "INTERACT"], [[0, 1], [0, 1]], ["INTERACT", [-1, 0]], [[0, 0], "INTERACT"], [[1, 0], [0, 0]], [[1, 0], [0, -1]], [[1, 0], [1, 0]], [[1, 0], [0, 1]], [[0, 0], [1, 0]], [[0, 0], [0, 1]], [[0, -1], [-1, 0]], [[0, -1], [1, 0]], [[0, 0], [0, -1]], [[0, -1], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], [0, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [0, 0]], [[0, 0], [1, 0]], ["INTERACT", [0, -1]], [[1, 0], [1, 0]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 1], [1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[0, 1], [-1, 0]], [[0, 1], [1, 0]], [[0, 0], [0, 1]], [[-1, 0], [1, 0]], [[-1, 0], "INTERACT"], [[0, 0], [0, 1]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], "INTERACT"], [[0, 0], [0, 1]], ["INTERACT", "INTERACT"], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[-1, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, -1], [-1, 0]], [[0, -1], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[0, -1], [0, -1]], [[1, 0], [0, -1]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], ["INTERACT", [0, -1]], [[0, 0], [1, 0]], [[1, 0], [0, -1]], [[1, 0], [1, 0]], [[1, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 1], [-1, 0]], [[0, 1], [-1, 0]], [[0, 1], [0, 1]], [[0, 0], [0, 1]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[-1, 0], [0, 1]], [[-1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 1], [-1, 0]], [[0, 0], [0, -1]], ["INTERACT", [0, -1]], [[1, 0], [0, -1]], [[1, 0], [0, -1]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[0, -1], [1, 0]], [[0, -1], [0, -1]], [[0, 0], "INTERACT"], [[0, -1], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [0, 1]], [[-1, 0], [-1, 0]], [[0, 0], "INTERACT"], [[0, 0], [0, -1]], [[0, -1], [1, 0]], [[0, 0], [0, 0]], ["INTERACT", [1, 0]], [[1, 0], [0, 1]], [[1, 0], [0, -1]], [[0, 0], "INTERACT"], [[1, 0], [0, -1]], [[0, 1], [0, -1]], [[0, 0], "INTERACT"], [[0, 1], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], [1, 0]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 1], [0, 1]], ["INTERACT", [-1, 0]], [[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[-1, 0], [0, 1]], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, -1], [0, 1]], [[0, -1], [0, 1]], [[0, -1], [0, 1]], [[0, -1], [0, 1]], [[0, 0], [-1, 0]], [[1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, 0], "INTERACT"], [[0, -1], [0, -1]], ["INTERACT", [-1, 0]], [[0, 0], [0, 1]], [[-1, 0], [-1, 0]], [[-1, 0], [0, 1]], [[0, 0], [0, 1]], [[-1, 0], [0, -1]], [[0, 0], [0, 1]], [[0, -1], [0, 1]], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[1, 0], [0, 1]], [[1, 0], [0, 1]], [[0, 0], [1, 0]], [[0, 0], [0, 1]], [[0, -1], [-1, 0]], [[0, 0], [0, 1]], ["INTERACT", "INTERACT"], [[1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[1, 0], [0, -1]], [[1, 0], [0, -1]], [[0, 0], [1, 0]], [[0, 1], [1, 0]], [[0, 1], [0, -1]], [[0, 1], "INTERACT"], [[0, 0], [-1, 0]], [[0, 1], [-1, 0]], [[0, 0], [0, 1]], [[-1, 0], [0, 0]], [[-1, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], "INTERACT"], [[0, 0], [-1, 0]], ["INTERACT", [0, -1]], [[1, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, -1], [0, -1]], [[1, 0], [-1, 0]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[1, 0], [0, 0]], [[0, 0], [1, 0]], [[1, 0], [0, -1]], [[0, 0], [-1, 0]], [[0, -1], [0, 0]], [[0, 0], [0, -1]], [[0, -1], "INTERACT"], [[0, -1], [-1, 0]], [[0, -1], [1, 0]], [[-1, 0], [0, -1]], [[0, 0], [0, 0]], [[-1, 0], "INTERACT"], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [0, 0]], ["INTERACT", [1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[1, 0], [0, -1]], [[1, 0], [-1, 0]], [[0, 0], [0, 0]], [[0, 1], [0, 1]], [[0, 1], [0, 1]], [[0, 1], [0, -1]], [[0, 1], "INTERACT"], [[-1, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[0, 1], [0, -1]], ["INTERACT", "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], [0, -1]], [[-1, 0], [0, 1]], [[-1, 0], [0, 1]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[0, -1], [0, 1]], [[0, -1], [1, 0]], [[0, -1], [0, 0]], [[0, -1], [1, 0]], [[0, 0], [0, -1]], [[1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], [-1, 0]], [[1, 0], [-1, 0]], [[0, -1], [-1, 0]], [[0, 0], "INTERACT"], ["INTERACT", [0, -1]], [[0, 0], [1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[-1, 0], [0, -1]], [[0, 0], [1, 0]], [[0, 1], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], "INTERACT"], [[0, 0], [0, -1]], [[1, 0], [-1, 0]], [[1, 0], "INTERACT"], [[1, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 1], [0, -1]], ["INTERACT", "INTERACT"], [[0, 0], [0, 0]], [[-1, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[-1, 0], [0, 1]], [[0, 0], [-1, 0]], [[-1, 0], [0, 1]], [[0, 0], [0, 0]], [[0, 0], [0, 0]], [[0, 0], [0, 0]], [[1, 0], [-1, 0]], [[0, 0], [0, -1]], [[1, 0], [0, 1]], [[1, 0], "INTERACT"], [[1, 0], [-1, 0]], [[0, 0], "INTERACT"], [[1, 0], [0, -1]], [[1, 0], [1, 0]], [[0, -1], [1, 0]], [[0, -1], [1, 0]], [[0, -1], "INTERACT"], [[0, 0], [0, -1]], [[0, -1], "INTERACT"], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], [1, 0]], [[0, 1], [1, 0]], [[0, 1], [0, -1]], [[0, 1], [1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[-1, 0], [1, 0]], [[-1, 0], [1, 0]], [[-1, 0], [0, 1]], [[-1, 0], [1, 0]], [[-1, 0], "INTERACT"], [[0, 0], [1, 0]], [[0, -1], [1, 0]], [[0, -1], [1, 0]], [[0, -1], [1, 0]], [[0, -1], [0, 1]], [[1, 0], [1, 0]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[0, -1], [1, 0]], [[0, 0], [1, 0]], [[0, -1], [1, 0]], [[0, 0], [1, 0]], ["INTERACT", [0, -1]], [[0, 0], [0, 0]], [[1, 0], [0, 1]], [[0, 0], [0, 1]], [[1, 0], [-1, 0]], [[1, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, 1], "INTERACT"], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[0, 0], [0, 0]], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], ["INTERACT", "INTERACT"], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, -1], [-1, 0]], [[0, 1], [-1, 0]], [[0, 0], [0, -1]], [[0, 1], [0, -1]], [[0, 1], [1, 0]], [[0, 1], [1, 0]], [[-1, 0], [0, -1]], [[0, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], [0, 1]], [[0, 0], [-1, 0]], ["INTERACT", "INTERACT"], [[1, 0], [0, -1]], [[1, 0], [-1, 0]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[0, -1], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[0, -1], "INTERACT"], [[-1, 0], [0, -1]], [[-1, 0], "INTERACT"], [[0, -1], "INTERACT"], [[0, 0], "INTERACT"], [[0, -1], "INTERACT"], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[1, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 1], [1, 0]], [[0, 1], [0, 1]], [[0, 0], [1, 0]], [[-1, 0], [1, 0]], [[-1, 0], "INTERACT"], [[-1, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, 1], [-1, 0]], ["INTERACT", [0, 1]], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [0, 1]], [[-1, 0], [0, 1]], [[0, -1], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, -1], [-1, 0]], [[0, -1], [0, -1]], [[0, -1], [0, -1]], [[0, 0], [0, -1]], [[1, 0], [0, -1]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[1, 0], "INTERACT"], [[1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, 1]], [[0, 1], [0, 1]], [[0, 1], [1, 0]], [[0, 0], [1, 0]], [[0, 1], [0, 1]], [[-1, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 1], [-1, 0]], ["INTERACT", [0, -1]], [[0, 0], [0, -1]], [[1, 0], [1, 0]], [[1, 0], [0, -1]], [[1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, -1], [-1, 0]], [[0, -1], [0, -1]], [[0, -1], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[-1, 0], [0, 1]], [[0, 0], [0, -1]], [[0, -1], [0, 0]], [[0, 0], [0, 0]], [[1, 0], [0, 1]], [[0, 0], [-1, 0]], [[1, 0], [0, 0]], [[0, 0], "INTERACT"], [[0, -1], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[-1, 0], [0, -1]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, -1], [0, -1]], [[0, 0], "INTERACT"], ["INTERACT", "INTERACT"], [[1, 0], [1, 0]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 1], [1, 0]], [[0, 0], [0, 1]], [[0, 1], [0, 1]], [[0, 1], [1, 0]], [[0, 0], [1, 0]], [[-1, 0], "INTERACT"], [[-1, 0], [0, -1]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], [-1, 0]], ["INTERACT", [-1, 0]], [[0, 0], [-1, 0]], [[1, 0], [0, 1]], [[1, 0], [0, 1]], [[1, 0], [1, 0]], [[0, 0], [0, 0]], [[1, 0], [0, 1]], [[0, -1], [0, 1]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[0, -1], [0, 0]], [[0, 0], [0, 0]], [[-1, 0], "INTERACT"], [[0, 0], [0, 1]], [[0, 0], [0, 0]], [[0, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[0, 0], [0, -1]], [[-1, 0], [0, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[0, -1], [1, 0]], [[0, 0], [0, 0]], [[1, 0], "INTERACT"], [[0, 0], [0, 0]], [[0, 0], [0, 0]], [[0, 0], [0, 0]], [[0, 0], [0, 1]], [[0, 0], [1, 0]], ["INTERACT", [0, 0]], [[0, 0], [0, 0]], [[0, 0], [0, 1]], [[0, -1], [0, -1]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], ["INTERACT", [0, 0]], [[1, 0], [0, -1]], [[0, 0], [-1, 0]], [[1, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 1], [0, -1]], [[0, 1], "INTERACT"], [[0, 1], [0, -1]], [[0, 1], [0, 1]], [[0, 0], [-1, 0]], [[0, 0], "INTERACT"], [[-1, 0], [0, -1]], [[0, 0], [1, 0]], [[-1, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], [1, 0]], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 1], "INTERACT"], [[0, 0], "INTERACT"], ["INTERACT", [1, 0]], [[-1, 0], [1, 0]], [[-1, 0], [0, 1]], [[0, 0], [1, 0]], [[-1, 0], "INTERACT"], [[-1, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, -1], [-1, 0]], [[0, -1], [0, 1]], [[0, 0], "INTERACT"], [[0, -1], [-1, 0]], [[0, -1], [-1, 0]], [[0, 0], [-1, 0]], [[1, 0], [0, -1]], [[0, 0], [0, -1]], [[1, 0], [1, 0]], [[0, -1], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], ["INTERACT", [0, -1]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, 1]], [[0, 1], [0, 1]], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 1], "INTERACT"], [[0, 0], [-1, 0]], [[0, 1], [-1, 0]], [[0, 1], [0, -1]], [[-1, 0], [0, -1]], [[-1, 0], [1, 0]], [[-1, 0], [1, 0]], [[0, 0], [0, -1]], ["INTERACT", "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 1], [-1, 0]], [[0, 0], [0, 1]], ["INTERACT", "INTERACT"], [[1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[1, 0], "INTERACT"], [[1, 0], [0, -1]], [[1, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, -1], [1, 0]], [[0, -1], [0, -1]], [[0, -1], "INTERACT"], [[0, -1], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[-1, 0], [0, 0]], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, -1], [0, -1]], [[0, 0], [0, -1]], ["INTERACT", [1, 0]], [[1, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 1], [1, 0]], [[0, 1], [0, 1]], [[0, 0], [1, 0]], [[0, 1], "INTERACT"], [[-1, 0], [0, -1]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 1], [-1, 0]], ["INTERACT", [-1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[1, 0], [0, -1]], [[1, 0], [0, 1]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[0, -1], [1, 0]], [[0, -1], [1, 0]], [[0, 0], [0, 1]], [[0, -1], [0, 1]], [[-1, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [-1, 0]], [[0, 0], [0, -1]], ["INTERACT", [-1, 0]], [[1, 0], [1, 0]], [[1, 0], [1, 0]], [[0, 0], [0, -1]], [[1, 0], "INTERACT"], [[0, 1], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], [0, 1]], [[0, 1], [0, 1]], [[-1, 0], [1, 0]], [[-1, 0], [0, 1]], [[0, 0], [0, 1]], [[-1, 0], [0, 1]], [[0, 1], [1, 0]], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[1, 0], "INTERACT"], [[0, 0], [0, 0]], [[1, 0], [-1, 0]], [[0, -1], [-1, 0]], [[0, -1], [0, -1]], [[0, 0], [0, -1]], [[0, -1], [0, -1]], [[-1, 0], [0, -1]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [0, 0]], [[0, -1], [0, 0]], [[0, 0], [0, 0]], ["INTERACT", [0, 0]], [[1, 0], [0, -1]], [[1, 0], "INTERACT"], [[0, 0], [0, -1]], [[1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], [0, 1]], [[0, 1], [-1, 0]], [[0, 1], [0, 0]], [[0, 0], "INTERACT"], [[-1, 0], [0, -1]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[-1, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], [0, 0]], [[0, 0], "INTERACT"], [[0, 1], "INTERACT"], [[0, 0], "INTERACT"], ["INTERACT", [0, -1]], [[-1, 0], "INTERACT"], [[-1, 0], [1, 0]], [[0, 0], [0, -1]], [[-1, 0], [0, 1]], [[-1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, -1], [0, 1]], [[0, -1], [-1, 0]], [[0, 0], [-1, 0]], [[0, -1], "INTERACT"], [[1, 0], [0, -1]], [[0, 0], [0, 1]], [[1, 0], [1, 0]], [[0, 0], "INTERACT"], [[1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, -1], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[0, 0], [1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [-1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [0, 0]], [[-1, 0], [0, 1]], [[0, 0], "INTERACT"], [[-1, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, -1], "INTERACT"], ["INTERACT", "INTERACT"], [[1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[1, 0], [0, 1]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[0, 0], [0, 0]], [[0, 0], [0, 0]], [[0, 0], "INTERACT"], [[-1, 0], [0, -1]], [[0, 0], [0, 0]], [[0, -1], [0, 0]], [[0, 0], [0, 0]], [[0, 0], [0, 0]], [[1, 0], [0, 1]], ["INTERACT", [1, 0]], [[0, 0], [0, 0]], [[1, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], [0, 0]], [[0, 0], "INTERACT"], [[0, -1], "INTERACT"], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], ["INTERACT", [0, 1]], [[1, 0], "INTERACT"], [[0, 0], [-1, 0]], [[1, 0], [-1, 0]], [[1, 0], [0, -1]], [[0, 1], "INTERACT"], [[0, 0], [1, 0]], [[0, 1], [1, 0]], [[0, 1], [0, -1]], [[-1, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], [1, 0]], [[-1, 0], [1, 0]], [[0, 1], "INTERACT"], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], [0, 0]], ["INTERACT", [0, 0]], [[-1, 0], [-1, 0]], [[0, 0], [0, 1]], [[-1, 0], [0, 1]], [[0, 0], [0, 0]], [[0, 0], "INTERACT"], [[-1, 0], [0, 0]], [[0, 0], "INTERACT"], [[0, 0], [0, 1]], [[0, 0], [1, 0]], [[0, -1], "INTERACT"], [[0, -1], "INTERACT"], [[0, -1], "INTERACT"], [[0, 0], [0, -1]], [[0, -1], "INTERACT"], [[1, 0], "INTERACT"], [[1, 0], [0, 1]], [[0, 0], "INTERACT"], [[1, 0], [0, 0]], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 1], "INTERACT"], [[0, 0], [1, 0]], [[0, 1], [0, 1]], [[0, 1], [0, 1]], [[0, 1], [0, 1]], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], [0, 1]], [[0, 0], [0, 1]], [[0, 1], [-1, 0]], [[1, 0], "INTERACT"], [[1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[1, 0], "INTERACT"], [[1, 0], [0, 1]], [[1, 0], "INTERACT"], [[1, 0], "INTERACT"], [[0, 0], [0, 0]], [[0, -1], "INTERACT"], [[0, 0], "INTERACT"], [[0, -1], "INTERACT"], [[0, -1], "INTERACT"], [[-1, 0], [1, 0]], [[0, 0], "INTERACT"], [[-1, 0], [0, -1]], [[0, 0], [0, 0]], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], "INTERACT"], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[-1, 0], [0, 0]], [[0, -1], "INTERACT"], [[0, 0], [0, 1]], [[0, 0], "INTERACT"], ["INTERACT", [1, 0]], [[0, 0], [-1, 0]], [[1, 0], [1, 0]], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, 0]], [[0, 1], [1, 0]], [[0, 0], [0, 0]], ["INTERACT", [-1, 0]], [[1, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, 0], "INTERACT"], [[0, 0], [0, 1]], [[0, 1], "INTERACT"], [[0, 0], "INTERACT"], [[1, 0], "INTERACT"], [[0, 0], "INTERACT"], ["INTERACT", "INTERACT"], [[0, 0], [0, 1]], [[0, 1], [0, -1]], [[0, 0], [0, 0]], [[0, 1], [0, 0]], [[-1, 0], [0, 1]], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], [[0, 1], [-1, 0]], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[0, 0], [0, -1]], [[0, 0], [0, 0]], [[0, 0], [1, 0]], [[0, 0], [-1, 0]], [[0, 0], "INTERACT"], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[0, 0], [-1, 0]], [[0, 0], [0, -1]], ["INTERACT", [0, 1]], [[0, 0], [0, -1]], [[0, 0], [0, -1]], [[0, 0], [0, 1]], [[0, 0], [0, -1]], [[0, 0], [1, 0]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[-1, 0], "INTERACT"], [[0, 0], "INTERACT"], [[-1, 0], [-1, 0]], [[0, 0], [0, 0]], [[0, 0], [0, 1]], [[0, -1], [0, 1]], [[0, -1], [1, 0]], [[0, 0], "INTERACT"], [[0, -1], "INTERACT"], [[0, 0], [0, -1]], [[0, 0], "INTERACT"], [[0, 0], [1, 0]], [[0, 0], [1, 0]], [[0, 0], "INTERACT"], [[0, 0], [1, 0]], [[1, 0], [1, 0]], [[1, 0], [0, 1]], [[1, 0], [0, 1]], [[0, 0], "INTERACT"], [[0, 0], [-1, 0]], [[0, -1], [0, 1]], [[0, 0], [0, 1]], [[-1, 0], [-1, 0]], [[0, 0], [0, 1]], ["INTERACT", [-1, 0]], [[-1, 0], [0, 0]], [[0, 0], [0, 1]], [[1, 0], [0, 1]], [[0, 0], "INTERACT"], [[1, 0], [0, 1]], [[0, 0], [0, 0]], [[0, 0], [0, 1]], [[0, -1], [-1, 0]], [[0, 0], [0, 1]], [[0, 0], [0, 1]], ["INTERACT", [0, 1]], [[0, 0], [0, 1]], [[1, 0], [-1, 0]], [[1, 0], [0, 1]], [[1, 0], [0, -1]], [[0, 0], [-1, 0]], [[0, 0], [-1, 0]], ["INTERACT", "INTERACT"], [[0, 0], [0, 0]], [[0, 1], [0, 1]], [[0, 0], [-1, 0]], [[1, 0], [-1, 0]], [[0, 0], "INTERACT"], ["INTERACT", [0, 0]], [[0, 0], [-1, 0]], [[0, 1], [0, -1]], [[0, 0], "INTERACT"], [[0, 1], [0, 1]], [[-1, 0], [0, 1]], [[0, 0], [0, -1]], [[-1, 0], "INTERACT"], [[0, 1], [-1, 0]], [[0, 0], "INTERACT"], [[0, 1], [0, -1]], [[0, 0], [1, 0]], ["INTERACT", [1, 0]], [[0, 0], [0, 0]], [[1, 0], [0, 0]], [[1, 0], [0, 1]], [[0, 0], [0, 0]], [[1, 0], [0, -1]], [[0, -1], "INTERACT"], [[0, -1], [1, 0]], [[0, -1], [0, 1]], [[0, 0], [-1, 0]], [[-1, 0], [0, 1]], [[0, 0], [1, 0]], [[0, 0], [-1, 0]], [[-1, 0], [1, 0]], [[-1, 0], [1, 0]], [[0, 0], [0, 1]], [[0, 0], [1, 0]], [[0, 0], [0, 1]], [[-1, 0], [0, 1]], [[0, 0], [-1, 0]], [[-1, 0], [0, 1]], [[0, 0], [0, -1]], [[-1, 0], [1, 0]], [[0, 0], [1, 0]], [[0, -1], [1, 0]], [[0, 0], [0, 1]], ["INTERACT", [1, 0]], [[1, 0], [1, 0]], [[0, 0], [0, 1]], [[1, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, 0], [-1, 0]], [[0, 0], [1, 0]], [[0, 1], [-1, 0]], [[0, 0], [1, 0]], [[0, 1], [0, 1]]]], "mdp_params": [{"layout_name": "counter_circuit", "num_items_for_soup": 3, "rew_shaping_params": null, "cook_time": 20, "start_order_list": null}]}