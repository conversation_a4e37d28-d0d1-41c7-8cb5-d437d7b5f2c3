# 🧪 Experiment2Data 分组分析总结

## 📊 总体概况

- **分析时间**: 2025-06-08 00:51:44
- **总组数**: 40个组
- **总文件数**: 162个文件
- **成功分析**: 162/162 (100%)
- **Excel报告**: `Experiment2Data_Analysis_20250608_005144.xlsx`

## 📁 数据分组结构

### 组织方式
- **组编号**: 1-40 (共40个组)
- **每组文件**: 通常4个文件，部分组有5个文件
- **文件命名**: `[组号][条件].json` (如: 1dasy.json, 2gcc.json)
- **排除文件**: 所有 `*lx.json` 文件已被排除

### 实验条件分类
每个组包含以下4种主要条件：

| 条件代码 | 条件名称 | 布局类型 | 文件数量 | 平均效率 | 平均得分 |
|----------|----------|----------|----------|----------|----------|
| **D-ASY** | Direct ASY | ASY | 40 | 48.86% | 379分 |
| **D-CC** | Direct CC | CC | 40 | 68.82% | 158分 |
| **G-ASY** | Guided ASY | ASY | 40 | 47.81% | 342分 |
| **G-CC** | Guided CC | CC | 40 | 69.44% | 186分 |

### 特殊文件
- **组7**: 包含 `7lx2.json` (Unknown布局)
- **组10**: 包含 `5_17_2025_16_2_23_ppo_bc_1.json` (Unknown布局)

## 📈 关键发现

### 1. 布局效率对比
- **CC布局** 明显优于 **ASY布局**
  - CC平均效率: 69.13%
  - ASY平均效率: 48.34%
  - 效率差异: +20.79%

### 2. 得分表现对比
- **ASY布局** 得分明显高于 **CC布局**
  - ASY平均得分: 360.5分
  - CC平均得分: 172分
  - 得分差异: +188.5分

### 3. 引导方式影响
- **Direct vs Guided** 差异较小
  - D-ASY vs G-ASY: 效率差异仅1.05%
  - D-CC vs G-CC: 效率差异仅0.62%

### 4. 效率-得分权衡
- **高效率低得分**: CC布局交互精准但总产出较低
- **低效率高得分**: ASY布局交互频繁但总产出较高

## 📋 详细分组信息

### 标准组 (36个组)
组1-6, 8-9, 11-40，每组包含4个文件：
- `[组号]dasy.json` - D-ASY条件
- `[组号]dcc.json` - D-CC条件  
- `[组号]gasy.json` - G-ASY条件
- `[组号]gcc.json` - G-CC条件

### 特殊组 (4个组)

#### 组7 (5个文件)
- `7dasy.json`, `7dcc.json`, `7gasy.json`, `7gcc.json`
- `7lx2.json` - 特殊实验条件 (Unknown布局，效率15.28%)

#### 组10 (5个文件)  
- `10dasy.json`, `10dcc.json`, `10gasy.json`, `10gcc.json`
- `5_17_2025_16_2_23_ppo_bc_1.json` - PPO-BC实验 (Unknown布局，效率75.68%)

## 📊 统计分析

### 按条件统计
```
D-ASY: 40文件, 平均效率48.86%, 平均得分379分
D-CC:  40文件, 平均效率68.82%, 平均得分158分  
G-ASY: 40文件, 平均效率47.81%, 平均得分342分
G-CC:  40文件, 平均效率69.44%, 平均得分186分
```

### 效率分布
- **高效率组** (>80%): 主要为CC条件
- **中效率组** (50-80%): 混合条件
- **低效率组** (<50%): 主要为ASY条件

### 得分分布
- **高得分组** (>400分): 主要为ASY条件
- **中得分组** (200-400分): 混合条件  
- **低得分组** (<200分): 主要为CC条件

## 🎯 实验设计洞察

### 实验变量
1. **布局类型**: ASY vs CC (主要变量)
2. **引导方式**: Direct vs Guided (次要变量)
3. **组间差异**: 个体或环境差异

### 平衡设计
- 每个组都包含所有4种条件
- 40个组提供充足的重复实验
- 排除了可能的干扰文件(*lx.json)

### 数据质量
- **100%成功率**: 所有162个文件都成功分析
- **一致性**: 文件命名和结构规范
- **完整性**: 覆盖所有实验条件

## 📁 Excel报告结构

生成的Excel文件包含4个工作表：

1. **详细分析结果**: 每个文件的完整分析数据
2. **按组汇总**: 40个组的汇总统计
3. **按条件汇总**: 4种条件的对比分析  
4. **按布局汇总**: ASY vs CC布局对比

## 🔍 后续分析建议

### 统计分析
1. **方差分析**: 检验布局和引导方式的主效应
2. **配对t检验**: 比较同组内不同条件的差异
3. **相关分析**: 探索效率与得分的关系

### 深度分析
1. **学习曲线**: 分析组间的表现变化趋势
2. **异常值检测**: 识别表现异常的组或条件
3. **交互效应**: 探索布局×引导方式的交互作用

### 可视化建议
1. **箱线图**: 展示各条件的效率和得分分布
2. **散点图**: 显示效率-得分关系
3. **热力图**: 展示40个组的表现模式

---

**📊 数据文件**: `Experiment2Data_Analysis_20250608_005144.xlsx`  
**🔧 分析工具**: `analyze_experiment2_data.py`  
**📅 分析日期**: 2025-06-08
