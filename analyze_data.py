#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPSS数据整理脚本 - 分析游戏交互数据
分析指标：
1. 每轮得分：ep_rewards
2. 交互：ep_actions 中的有效交互次数和无效交互次数
"""

import json
import csv
from typing import Dict, List, Tuple, Any

def load_data(file_path: str) -> Dict[str, Any]:
    """加载JSON数据文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"加载数据文件失败: {e}")
        return {}

def analyze_data_structure(data: Dict[str, Any]) -> None:
    """分析数据结构"""
    print("=== 数据结构分析 ===")
    print(f"数据的主要键: {list(data.keys())}")
    
    for key, value in data.items():
        if isinstance(value, list):
            print(f"{key}: 列表，长度 = {len(value)}")
            if len(value) > 0:
                print(f"  第一个元素类型: {type(value[0])}")
                if isinstance(value[0], list) and len(value[0]) > 0:
                    print(f"  第一个子元素类型: {type(value[0][0])}")
        else:
            print(f"{key}: {type(value)}")

def analyze_interactions(observations: List[List[Dict]], actions: List[List] = None) -> Dict[str, Dict[str, int]]:
    """
    分析有效交互和无效交互次数，分别统计每个玩家的表现

    基于动作序列中的INTERACT命令，判断每个交互是否有效

    有效交互包括：
    - 在物品生成点拾取物品（手为空）
    - 往锅里放食材（锅未满）
    - 从锅里取汤（手持盘子且汤已完成）
    - 在送餐区提交订单
    - 在空的台面放置物品

    无效交互包括：
    - 对着墙壁或不可交互物体按交互键
    - 手满时尝试拾取
    - 往满锅放食材
    - 取未完成的汤
    - 在错误位置提交订单

    返回格式：
    {
        'player0': {'valid': int, 'invalid': int},  # Human Keyboard Input
        'player1': {'valid': int, 'invalid': int},  # Human-aware PPO agent
        'total': {'valid': int, 'invalid': int}
    }
    """
    # 初始化统计数据
    stats = {
        'player0': {'valid': 0, 'invalid': 0},  # Human Keyboard Input
        'player1': {'valid': 0, 'invalid': 0},  # Human-aware PPO agent
        'total': {'valid': 0, 'invalid': 0}
    }

    if not observations or not actions:
        return stats

    # 定义关键位置（根据实际布局调整）
    ITEM_SPAWN_POINTS = [(1, 0), (3, 0)]  # 洋葱生成点坐标
    DISH_SPAWN_POINTS = [(2, 3)]          # 盘子生成点坐标
    SERVING_POINTS = [(3, 2)]             # 送餐区坐标
    COUNTER_POSITIONS = [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (3, 2)]  # 可放置物品的位置

    def is_adjacent(pos1, pos2):
        """判断两个位置是否相邻（曼哈顿距离=1）"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1]) == 1

    def is_interact_valid(timestep_idx, player_idx):
        """判断指定时间步和玩家的INTERACT是否有效"""
        if timestep_idx >= len(observations[0]):
            return False

        obs = observations[0][timestep_idx]  # 第一轮游戏
        if player_idx >= len(obs.get("players", [])):
            return False

        player = obs["players"][player_idx]
        pos = tuple(player["position"])  # 转换为元组便于比较
        held = player.get("held_object")

        # 1. 检查灶台交互
        for obj in obs.get("objects", []):
            if obj["name"] == "soup" and is_adjacent(pos, tuple(obj["position"])):
                soup_state = obj["state"]
                # 尝试放入洋葱
                if held and held["name"] == "onion":
                    return soup_state[1] < 3  # 灶台未满则有效
                # 尝试取汤
                elif held and held["name"] == "dish":
                    return soup_state[2] == 20  # 汤已完成
                # 无效操作
                return False

        # 2. 检查物品拾取
        if not held:
            # 检查洋葱拾取
            if pos in ITEM_SPAWN_POINTS:
                return True
            # 检查盘子拾取
            if pos in DISH_SPAWN_POINTS:
                return True

        # 3. 检查订单提交
        if held and held["name"] == "soup" and pos in SERVING_POINTS:
            return True

        # 4. 检查放下物品
        if held and pos in COUNTER_POSITIONS:
            # 确保目标位置没有其他物品
            for obj in obs.get("objects", []):
                if tuple(obj["position"]) == pos:
                    return False
            return True

        return False  # 默认无效

    # 主统计逻辑
    if len(actions) > 0 and len(actions[0]) > 0:
        episode_actions = actions[0]  # 第一轮游戏的动作

        for timestep_idx, timestep_actions in enumerate(episode_actions):
            for player_idx, action in enumerate(timestep_actions):
                if action == "INTERACT":
                    player_key = f'player{player_idx}'
                    if is_interact_valid(timestep_idx, player_idx):
                        stats[player_key]['valid'] += 1
                        stats['total']['valid'] += 1
                    else:
                        stats[player_key]['invalid'] += 1
                        stats['total']['invalid'] += 1

    return stats

def extract_rewards(data: Dict[str, Any]) -> List[float]:
    """提取每轮得分"""
    rewards = []
    
    if 'ep_rewards' in data:
        ep_rewards = data['ep_rewards']
        if isinstance(ep_rewards, list):
            for episode_rewards in ep_rewards:
                if isinstance(episode_rewards, list):
                    # 计算每轮的总得分
                    total_reward = sum(episode_rewards)
                    rewards.append(total_reward)
                else:
                    rewards.append(episode_rewards)
    
    return rewards

def main():
    """主函数"""
    print("=== SPSS数据整理工具 ===")
    print("分析游戏交互数据，提取有效交互次数和得分信息\n")
    
    # 加载数据
    data = load_data('test5.json')
    if not data:
        print("无法加载数据文件")
        return
    
    # 分析数据结构
    analyze_data_structure(data)
    print()
    
    # 提取观察数据
    observations = data.get('ep_observations', [])
    actions = data.get('ep_actions', [])
    
    # 分析交互
    interaction_stats = analyze_interactions(observations, actions)

    # 提取得分
    rewards = extract_rewards(data)

    # 输出结果
    print("=== 分析结果 ===")
    print("总体统计:")
    total_valid = interaction_stats['total']['valid']
    total_invalid = interaction_stats['total']['invalid']
    total_interactions = total_valid + total_invalid

    print(f"  有效交互次数: {total_valid}")
    print(f"  无效交互次数: {total_invalid}")
    print(f"  总交互次数: {total_interactions}")
    print(f"  有效交互比例: {total_valid / total_interactions * 100:.2f}%" if total_interactions > 0 else "无交互数据")

    print("\nPlayer0 (Human Keyboard Input) 统计:")
    p0_valid = interaction_stats['player0']['valid']
    p0_invalid = interaction_stats['player0']['invalid']
    p0_total = p0_valid + p0_invalid
    print(f"  有效交互次数: {p0_valid}")
    print(f"  无效交互次数: {p0_invalid}")
    print(f"  总交互次数: {p0_total}")
    print(f"  有效交互比例: {p0_valid / p0_total * 100:.2f}%" if p0_total > 0 else "无交互数据")

    print("\nPlayer1 (Human-aware PPO agent) 统计:")
    p1_valid = interaction_stats['player1']['valid']
    p1_invalid = interaction_stats['player1']['invalid']
    p1_total = p1_valid + p1_invalid
    print(f"  有效交互次数: {p1_valid}")
    print(f"  无效交互次数: {p1_invalid}")
    print(f"  总交互次数: {p1_total}")
    print(f"  有效交互比例: {p1_valid / p1_total * 100:.2f}%" if p1_total > 0 else "无交互数据")
    
    if rewards:
        print(f"\n每轮得分统计:")
        print(f"  轮数: {len(rewards)}")
        print(f"  总得分: {sum(rewards)}")
        print(f"  平均得分: {sum(rewards) / len(rewards):.2f}")
        print(f"  最高得分: {max(rewards)}")
        print(f"  最低得分: {min(rewards)}")
        print(f"  得分列表: {rewards}")
    else:
        print("\n未找到得分数据")
    
    # 创建SPSS格式的数据
    spss_data = {
        # 总体统计
        '总有效交互次数': [total_valid],
        '总无效交互次数': [total_invalid],
        '总交互次数': [total_interactions],
        '总有效交互比例': [total_valid / total_interactions * 100 if total_interactions > 0 else 0],

        # Player0 (Human Keyboard Input) 统计
        'P0有效交互次数': [p0_valid],
        'P0无效交互次数': [p0_invalid],
        'P0总交互次数': [p0_total],
        'P0有效交互比例': [p0_valid / p0_total * 100 if p0_total > 0 else 0],

        # Player1 (Human-aware PPO agent) 统计
        'P1有效交互次数': [p1_valid],
        'P1无效交互次数': [p1_invalid],
        'P1总交互次数': [p1_total],
        'P1有效交互比例': [p1_valid / p1_total * 100 if p1_total > 0 else 0]
    }
    
    if rewards:
        spss_data.update({
            '轮数': [len(rewards)],
            '总得分': [sum(rewards)],
            '平均得分': [sum(rewards) / len(rewards)],
            '最高得分': [max(rewards)],
            '最低得分': [min(rewards)]
        })
    
    # 保存为CSV格式供SPSS使用
    with open('spss_data_test2.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = list(spss_data.keys())
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        # 由于所有值都是单元素列表，我们需要提取第一个元素
        row_data = {key: value[0] for key, value in spss_data.items()}
        writer.writerow(row_data)

    print(f"\n已保存SPSS数据到 spss_data_test2.csv")
    print("CSV文件内容:")
    for key, value in spss_data.items():
        print(f"{key}: {value[0]}")

if __name__ == "__main__":
    main()
