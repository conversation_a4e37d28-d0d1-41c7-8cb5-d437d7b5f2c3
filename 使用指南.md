# 🎮 游戏交互分析工具使用指南

## 📋 目录
1. [快速开始](#快速开始)
2. [添加新数据](#添加新数据)
3. [分析方法](#分析方法)
4. [输出文件说明](#输出文件说明)
5. [常见问题](#常见问题)

## 🚀 快速开始

### 方法1: 自动分析所有数据（最推荐）
```bash
python auto_batch_analyze.py
```
**优势**: 
- 🔍 自动发现data目录下的所有JSON文件
- 📊 生成完整的对比报告
- 💾 自动保存详细结果和汇总统计

### 方法2: 手动批量分析
```bash
python simple_batch_analyze.py
```
**适用**: 只分析预定义的文件列表

### 方法3: 单文件分析
```bash
# 分析您当前打开的文件
python analyze_universal.py "data/d-CC.json"

# 分析其他文件
python analyze_universal.py "data/g-asy.json"
```

## 📁 添加新数据

### 步骤1: 准备数据文件
将新的JSON数据文件放到 `data/` 目录下：

```
data/
├── d-asy copy.json     ✅ 已有
├── d-cc.json          ✅ 已有  
├── g-asy.json         ✅ 已有
├── g-cc.json          ✅ 已有
├── new-asy-data.json  🆕 新增ASY数据
├── experiment-cc.json 🆕 新增CC数据
└── test-data.json     🆕 其他数据
```

### 步骤2: 文件命名建议
- **ASY布局**: 文件名包含 `asy` 关键词（如 `xxx-asy.json`）
- **CC布局**: 文件名包含 `cc` 关键词（如 `xxx-cc.json`）
- **其他布局**: 工具会自动检测布局特征

### 步骤3: 运行分析
```bash
python auto_batch_analyze.py
```
工具会自动发现并分析所有新文件！

## 🔧 分析方法对比

| 方法 | 适用场景 | 优势 | 输出 |
|------|----------|------|------|
| `auto_batch_analyze.py` | 有多个数据文件 | 自动发现、完整报告 | 详细CSV + 汇总CSV |
| `simple_batch_analyze.py` | 固定文件列表 | 快速分析预定义文件 | 标准CSV报告 |
| `analyze_universal.py` | 单个文件分析 | 详细单文件信息 | 单文件CSV |

## 📊 输出文件说明

### 自动批量分析输出
- `auto_analysis_detailed.csv`: 每个文件的详细分析结果
- `auto_analysis_summary.csv`: 按布局类型的汇总统计

### 手动批量分析输出  
- `detailed_analysis_results.csv`: 详细结果
- `layout_summary.csv`: 布局对比

### 单文件分析输出
- `spss_data_asy.csv`: ASY布局文件结果
- `spss_data_cc.csv`: CC布局文件结果

## 📈 结果解读

### 关键指标说明
- **总交互次数**: 玩家执行INTERACT动作的总次数
- **有效交互次数**: 产生实际游戏效果的交互次数
- **交互效率**: 有效交互次数 / 总交互次数 × 100%
- **得分**: 游戏中获得的总分数
- **Player0**: Human Keyboard Input（人类玩家）
- **Player1**: Human-aware PPO agent（AI玩家）

### 布局特征
- **ASY布局**: 交互频繁，得分高，但效率相对较低
- **CC布局**: 交互效率高，但总交互次数和得分较低

## ❓ 常见问题

### Q1: 如何添加不是ASY/CC的新布局类型？
**A**: 编辑 `analyze_universal.py`：
1. 在 `get_layout_config()` 中添加新布局配置
2. 在 `detect_layout()` 中添加检测逻辑
3. 测试验证

### Q2: 数据文件格式要求？
**A**: JSON格式，包含以下必要字段：
```json
{
  "ep_observations": [...],  // 游戏状态观察
  "ep_actions": [...],       // 玩家动作序列  
  "ep_rewards": [...]        // 奖励/得分
}
```

### Q3: 如何处理分析失败的文件？
**A**: 
1. 检查文件格式是否正确
2. 确认文件路径是否存在
3. 查看错误信息，通常是数据格式问题

### Q4: 可以分析多轮游戏数据吗？
**A**: 可以，工具会自动处理多轮数据并计算平均值

### Q5: 如何自定义分析指标？
**A**: 修改 `analyze_interactions()` 函数，添加新的统计维度

## 🎯 推荐工作流程

### 日常分析
```bash
# 1. 将新数据放入data目录
cp new_game_data.json data/

# 2. 运行自动分析
python auto_batch_analyze.py

# 3. 查看结果
# - 控制台输出：实时分析结果
# - CSV文件：详细数据供进一步分析
```

### 深度分析
```bash
# 1. 先运行自动批量分析获得概览
python auto_batch_analyze.py

# 2. 针对特定文件进行详细分析
python analyze_universal.py "data/interesting_file.json"

# 3. 如需要，分析布局特征
python analyze_layouts.py
```

## 🔄 更新和扩展

### 添加新的分析维度
1. 修改 `analyze_interactions()` 函数
2. 更新CSV输出格式
3. 调整报告生成逻辑

### 支持新的数据格式
1. 修改 `load_data()` 函数
2. 适配数据提取逻辑
3. 测试兼容性

## 📞 技术支持

如果遇到问题：
1. 检查文件路径和格式
2. 查看控制台错误信息
3. 确认Python环境和依赖
4. 参考示例数据文件格式

---

**💡 提示**: 建议使用 `auto_batch_analyze.py` 作为主要分析工具，它能自动处理所有数据文件并生成完整的对比报告！
