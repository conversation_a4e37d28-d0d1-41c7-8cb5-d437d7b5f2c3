# 游戏交互分析工具 - ASY & CC 布局支持

## 概述

本工具支持ASY和CC两种游戏布局的自动检测和交互分析，能够准确识别不同布局类型并应用相应的分析配置。

## 支持的Layout类型

### 1. ASY Layout
- 布局范围: X(1-6), Y(1-3)
- 洋葱spawn点: [(1, 1), (1, 2), (5, 2)]
- 盘子spawn点: [(2, 2), (3, 3), (5, 3)]
- 锅位置: [(4, 2), (4, 3)]
- 送餐点: [(3, 2), (7, 1)]
- 特征: 锅位置在中间区域，交互频繁但效率相对较低

### 2. CC Layout  
- 布局范围: X(1-6), Y(1-3)
- 洋葱spawn点: [(1, 1), (2, 1), (3, 3), (4, 3)]
- 盘子spawn点: [(1, 2)]
- 锅位置: [(3, 0), (4, 0)]
- 送餐点: [(6, 2)]
- 特征: 锅位置在上方，交互效率高但总交互次数较少

## 使用方法

### 方法1: 单文件分析
```bash
# 分析ASY布局文件
python analyze_universal.py "data/d-asy copy.json"
python analyze_universal.py "data/g-asy.json"

# 分析CC布局文件  
python analyze_universal.py "data/d-cc.json"
python analyze_universal.py "data/g-cc.json"
```

### 方法2: 批量分析 (推荐)
```bash
python simple_batch_analyze.py
```

## 分析结果汇总

### ASY Layout 统计
- **文件数量**: 2个 (d-asy copy.json, g-asy.json)
- **总交互次数**: 1,837
- **总有效交互**: 821
- **平均效率**: 44.71%
- **平均得分**: 520分
- **Human玩家平均效率**: 64.80%
- **AI玩家平均效率**: 40.12%

### CC Layout 统计  
- **文件数量**: 2个 (d-cc.json, g-cc.json)
- **总交互次数**: 454
- **总有效交互**: 343
- **平均效率**: 75.55%
- **平均得分**: 280分
- **Human玩家平均效率**: 97.27%
- **AI玩家平均效率**: 68.78%

## 关键发现

### 布局对比分析
| 指标 | ASY布局 | CC布局 | 差异 |
|------|---------|--------|------|
| 平均效率(%) | 44.71 | 75.55 | -30.84 |
| 平均得分 | 520 | 280 | +240 |
| Human玩家效率(%) | 64.80 | 97.27 | -32.46 |
| AI玩家效率(%) | 40.12 | 68.78 | -28.66 |

### 重要结论
1. **CC布局交互效率更高**: CC布局的交互效率比ASY布局高30.8%
2. **ASY布局得分更高**: ASY布局的平均得分比CC布局高240分
3. **Human玩家在CC布局中表现更好**: 效率高达97.27%
4. **AI玩家在两种布局中都表现不如Human玩家**
5. **交互频率差异显著**: ASY布局交互次数是CC布局的4倍

## 技术特性

### 自动Layout检测
- **文件名检测**: 优先通过文件名中的'asy'或'cc'关键词识别
- **锅位置检测**: 通过锅的位置特征进行精确识别
  - ASY: 锅在(4,2), (4,3)
  - CC: 锅在(3,0), (4,0)
- **布局范围分析**: 作为备选检测方案

### 精确的交互检测
- **spawn点交互**: 支持多个洋葱和盘子spawn点
- **锅操作**: 检测放入食材、取汤等操作
- **送餐检测**: 支持多个delivery点
- **台面操作**: 检测物品放置和拾取

## 输出文件

### 单文件分析输出
- `spss_data_asy.csv`: ASY布局的SPSS数据
- `spss_data_cc.csv`: CC布局的SPSS数据

### 批量分析输出
- `detailed_analysis_results.csv`: 所有文件的详细分析结果
- `layout_summary.csv`: 按布局类型的汇总统计

## 文件说明

- `analyze_universal.py`: 通用分析工具，支持自动layout检测
- `simple_batch_analyze.py`: 批量分析工具，生成汇总报告
- `analyze_layouts.py`: Layout特征分析工具
- `README_final.md`: 完整使用说明

## 数据文件

- `data/d-asy copy.json`: ASY布局数据文件1
- `data/g-asy.json`: ASY布局数据文件2
- `data/d-cc.json`: CC布局数据文件1
- `data/g-cc.json`: CC布局数据文件2

## 扩展功能

### 添加新Layout支持
1. 在`get_layout_config()`中添加新配置
2. 在`detect_layout()`中添加检测逻辑
3. 测试验证新layout的识别和分析

### 自定义分析指标
- 修改`analyze_interactions()`函数
- 添加新的统计维度
- 扩展CSV输出格式

## 注意事项

1. 确保数据文件路径正确
2. CSV文件使用UTF-8-BOM编码，兼容SPSS导入
3. 批量分析工具会自动处理所有支持的文件
4. 建议使用批量分析工具获得完整的对比报告

## 性能优化建议

1. **ASY布局优化方向**:
   - 减少无效交互，提高操作精确度
   - 优化路径规划，减少不必要的移动
   - 改进AI算法，提高决策效率

2. **CC布局优势保持**:
   - 维持高效的交互模式
   - 继续优化Human-AI协作
   - 探索进一步提高得分的策略
