# SPSS数据整理工具

这个工具用于分析游戏交互数据，提取有效交互次数和得分信息，生成适合SPSS分析的数据格式。

## 功能说明

### 分析指标

1. **每轮得分 (ep_rewards)**
   - 提取每轮游戏的总得分
   - 计算平均得分、最高得分、最低得分
   - 统计游戏轮数

2. **交互分析 (ep_actions)**
   - **有效交互次数**：玩家按下"操作键"与场景中的物体发生有效接触
   - **无效交互次数**：无效操作（如对着空气按交互键）

### 有效交互定义

有效交互包括：
- **拿起/放下**：食材（洋葱）、盘子、煮好的汤
- **操作灶台**：往锅里放食材、从锅里取汤
- **提交订单**：把汤送到出菜口

不包括：
- 移动
- 转向
- 无效操作（如对着空气按交互键）

## 使用方法

### Python版本

```bash
python3 analyze_data.py
```

**依赖**：
- Python 3.x
- 标准库：json, csv, typing

### Node.js版本

```bash
node analyze_data.js
```

**依赖**：
- Node.js
- 标准库：fs, path

## 输出文件

两个版本都会生成CSV文件供SPSS使用：

- **Python版本**：`spss_data.csv`
- **Node.js版本**：`spss_data_node.csv`

### CSV文件结构

| 字段名 | 说明 |
|--------|------|
| 有效交互次数 | 玩家进行的有效交互总数 |
| 无效交互次数 | 玩家进行的无效交互总数 |
| 总交互次数 | 有效交互 + 无效交互 |
| 有效交互比例 | 有效交互次数 / 总交互次数 × 100% |
| 轮数 | 游戏进行的轮数 |
| 总得分 | 所有轮次得分之和 |
| 平均得分 | 总得分 / 轮数 |
| 最高得分 | 单轮最高得分 |
| 最低得分 | 单轮最低得分 |

## 分析结果示例

基于当前数据文件 `test.json` 的分析结果：

```
=== 分析结果 ===
有效交互次数: 954
无效交互次数: 0
总交互次数: 954
有效交互比例: 100.00%

每轮得分统计:
  轮数: 1
  总得分: 420
  平均得分: 420.00
  最高得分: 420
  最低得分: 420
```

### 数据解读

1. **交互效率极高**：有效交互比例达到100%，说明玩家的所有交互都是有意义的操作
2. **交互频繁**：单轮游戏中进行了954次有效交互，显示了高度的游戏参与度
3. **得分稳定**：单轮得分为420分

## 技术实现

### 交互检测算法

通过分析游戏状态的变化来识别交互：

1. **物品状态变化**：检测玩家手中物品的拿起/放下
2. **环境物品变化**：检测灶台上汤的状态变化（食材添加、烹饪进度）
3. **物品消失**：检测汤的提交（从环境中消失）

### 数据结构分析

输入数据包含：
- `ep_observations`：每步的游戏状态观察
- `ep_rewards`：每步的奖励值
- `ep_actions`：每步的动作序列
- `mdp_params`：MDP参数

## 文件说明

- `analyze_data.py`：Python版本的分析脚本
- `analyze_data.js`：Node.js版本的分析脚本
- `test.json`：输入的游戏数据文件
- `spss_data.csv`：Python版本生成的SPSS数据文件
- `spss_data_node.csv`：Node.js版本生成的SPSS数据文件
- `README.md`：本说明文档

## 注意事项

1. 确保输入文件 `test.json` 存在于同一目录下
2. 生成的CSV文件使用UTF-8编码，兼容SPSS导入
3. 两个版本的分析结果应该一致，可以交叉验证
4. 如需分析其他数据文件，请修改脚本中的文件名

## 扩展功能

如需添加更多分析指标，可以在脚本中扩展：
- 不同类型交互的分别统计
- 时间序列分析
- 玩家协作效率分析
- 错误操作模式识别
