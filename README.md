# 🎮 游戏交互分析工具

## 📁 项目结构

```
.
├── 📊 核心分析工具
│   ├── analyze_universal.py          # 通用单文件分析工具
│   ├── auto_batch_analyze.py         # 自动批量分析工具 (推荐)
│   └── analyze_single_detailed.py    # 详细单文件分析工具
│
├── 🔧 辅助工具
│   ├── simple_batch_analyze.py       # 手动批量分析工具
│   └── analyze_layouts.py            # 布局特征分析工具
│
├── 📚 文档
│   ├── README.md                     # 项目说明 (本文件)
│   └── 使用指南.md                    # 详细使用指南
│
└── 📂 数据目录
    └── data/
        ├── d-asy.json               # ASY布局数据文件1
        ├── g-asy.json               # ASY布局数据文件2  
        ├── d-cc.json                # CC布局数据文件1
        └── g-cc.json                # CC布局数据文件2
```

## 🚀 快速开始

### 推荐使用方法
```bash
# 自动分析所有数据文件
python auto_batch_analyze.py
```

### 其他使用方法
```bash
# 单文件分析
python analyze_universal.py "data/your-file.json"

# 详细单文件分析
python analyze_single_detailed.py "data/your-file.json"

# 手动批量分析
python simple_batch_analyze.py
```

## 📊 支持的布局类型

### ASY Layout
- 洋葱spawn点: [(1, 1), (1, 2), (5, 2)]
- 盘子spawn点: [(2, 2), (3, 3), (5, 3)]
- 锅位置: [(4, 2), (4, 3)]
- 送餐点: [(3, 2), (7, 1)]

### CC Layout
- 洋葱spawn点: [(1, 1), (2, 1), (3, 3), (4, 3)]
- 盘子spawn点: [(1, 2)]
- 锅位置: [(3, 0), (4, 0)]
- 送餐点: [(6, 2)]

## 📈 分析结果

### 当前数据分析结果
- **ASY布局**: 平均效率44.71%, 平均得分520分
- **CC布局**: 平均效率75.55%, 平均得分280分
- **Human玩家**在两种布局中都表现优于AI玩家

## 📁 添加新数据

1. 将新的JSON文件放入 `data/` 目录
2. 建议命名包含 `asy` 或 `cc` 关键词
3. 运行 `python auto_batch_analyze.py`

## 📋 输出文件

### 自动批量分析输出
- `auto_analysis_detailed.csv` - 详细分析结果
- `auto_analysis_summary.csv` - 汇总统计

### 单文件分析输出
- `spss_data_asy.csv` / `spss_data_cc.csv` - 标准分析结果
- `detailed_report_[文件名].csv` - 详细分析结果
- `detailed_report_[文件名].txt` - 文本报告

## 🔧 技术特性

- ✅ 自动布局检测 (ASY/CC)
- ✅ 智能交互分析
- ✅ 多文件批量处理
- ✅ SPSS兼容的CSV输出
- ✅ 详细的效率分析
- ✅ 玩家表现对比

## 📞 使用帮助

详细使用说明请参考 `使用指南.md`

---

**💡 提示**: 推荐使用 `auto_batch_analyze.py` 进行日常分析，它会自动发现并处理所有数据文件！
