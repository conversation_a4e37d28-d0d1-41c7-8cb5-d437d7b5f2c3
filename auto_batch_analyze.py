#!/usr/bin/env python3
"""
自动发现并分析所有数据文件的工具
"""
import os
import glob
import json
import csv
from analyze_universal import load_data, detect_layout, get_layout_config, analyze_interactions, extract_rewards

def find_data_files(data_dir='data'):
    """自动发现数据目录中的所有JSON文件"""
    if not os.path.exists(data_dir):
        print(f"数据目录 {data_dir} 不存在")
        return []
    
    # 查找所有JSON文件
    json_files = glob.glob(os.path.join(data_dir, '*.json'))
    
    print(f"在 {data_dir} 目录中发现 {len(json_files)} 个JSON文件:")
    for file in json_files:
        print(f"  - {file}")
    
    return json_files

def analyze_file(filename: str) -> dict:
    """分析单个文件"""
    print(f"\n正在分析: {filename}")
    
    # 加载数据
    data = load_data(filename)
    if not data:
        print(f"  ✗ 无法加载数据")
        return {'filename': filename, 'success': False, 'error': 'Failed to load data'}
    
    try:
        # 提取数据
        observations = data.get('ep_observations', [])
        actions = data.get('ep_actions', [])
        
        if not observations or not observations[0]:
            print(f"  ✗ 数据格式错误：缺少观察数据")
            return {'filename': filename, 'success': False, 'error': 'No observation data'}
        
        # 检测layout
        layout_type = detect_layout(observations, filename)
        config = get_layout_config(layout_type)
        
        print(f"  检测到布局类型: {layout_type}")
        
        # 分析交互
        interaction_stats = analyze_interactions(observations, actions, layout_type)
        
        # 提取得分
        rewards = extract_rewards(data)
        
        # 计算统计数据
        total_valid = interaction_stats['total']['valid']
        total_invalid = interaction_stats['total']['invalid']
        total_interactions = total_valid + total_invalid
        total_efficiency = (total_valid / total_interactions * 100) if total_interactions > 0 else 0
        
        p0_valid = interaction_stats['player0']['valid']
        p0_invalid = interaction_stats['player0']['invalid']
        p0_total = p0_valid + p0_invalid
        p0_efficiency = (p0_valid / p0_total * 100) if p0_total > 0 else 0
        
        p1_valid = interaction_stats['player1']['valid']
        p1_invalid = interaction_stats['player1']['invalid']
        p1_total = p1_valid + p1_invalid
        p1_efficiency = (p1_valid / p1_total * 100) if p1_total > 0 else 0
        
        total_score = sum(rewards) if rewards else 0
        
        result = {
            'filename': filename,
            'success': True,
            'layout_type': layout_type,
            'total_valid': total_valid,
            'total_invalid': total_invalid,
            'total_interactions': total_interactions,
            'total_efficiency': total_efficiency,
            'p0_valid': p0_valid,
            'p0_invalid': p0_invalid,
            'p0_total': p0_total,
            'p0_efficiency': p0_efficiency,
            'p1_valid': p1_valid,
            'p1_invalid': p1_invalid,
            'p1_total': p1_total,
            'p1_efficiency': p1_efficiency,
            'total_score': total_score,
            'episodes': len(observations) if observations else 0,
            'steps_per_episode': len(observations[0]) if observations and observations[0] else 0
        }
        
        print(f"  ✓ 布局: {layout_type}, 效率: {total_efficiency:.1f}%, 得分: {total_score}, 交互: {total_interactions}")
        return result
        
    except Exception as e:
        print(f"  ✗ 分析失败: {str(e)}")
        return {'filename': filename, 'success': False, 'error': str(e)}

def generate_report(results):
    """生成详细报告"""
    successful_results = [r for r in results if r['success']]
    
    if not successful_results:
        print("\n❌ 没有成功分析的文件")
        return
    
    print(f"\n=== 📊 汇总分析报告 ===")
    print(f"成功分析了 {len(successful_results)} 个文件")
    
    # 按layout类型分组
    layout_groups = {}
    for result in successful_results:
        layout = result['layout_type']
        if layout not in layout_groups:
            layout_groups[layout] = []
        layout_groups[layout].append(result)
    
    print(f"发现的布局类型: {list(layout_groups.keys())}")
    
    # 为每种布局生成统计
    layout_stats = {}
    for layout_type, group_results in layout_groups.items():
        if not group_results:
            continue
            
        total_interactions = sum(r['total_interactions'] for r in group_results)
        total_valid = sum(r['total_valid'] for r in group_results)
        avg_efficiency = sum(r['total_efficiency'] for r in group_results) / len(group_results)
        avg_score = sum(r['total_score'] for r in group_results) / len(group_results)
        avg_p0_eff = sum(r['p0_efficiency'] for r in group_results) / len(group_results)
        avg_p1_eff = sum(r['p1_efficiency'] for r in group_results) / len(group_results)
        
        layout_stats[layout_type] = {
            'file_count': len(group_results),
            'total_interactions': total_interactions,
            'total_valid': total_valid,
            'avg_efficiency': avg_efficiency,
            'avg_score': avg_score,
            'avg_p0_efficiency': avg_p0_eff,
            'avg_p1_efficiency': avg_p1_eff
        }
        
        print(f"\n=== {layout_type.upper()} 布局统计 ===")
        print(f"文件数量: {len(group_results)}")
        print(f"总交互次数: {total_interactions:,}")
        print(f"总有效交互: {total_valid:,}")
        print(f"平均效率: {avg_efficiency:.2f}%")
        print(f"平均得分: {avg_score:.0f}")
        print(f"Human玩家平均效率: {avg_p0_eff:.2f}%")
        print(f"AI玩家平均效率: {avg_p1_eff:.2f}%")
        
        print("详细数据:")
        for r in group_results:
            print(f"  {os.path.basename(r['filename'])}: 效率{r['total_efficiency']:.1f}%, 得分{r['total_score']}, 交互{r['total_interactions']}")
    
    # 布局对比（如果有多种布局）
    if len(layout_stats) > 1:
        print(f"\n=== 🔄 布局对比分析 ===")
        layout_names = list(layout_stats.keys())
        
        print(f"{'指标':<25}", end='')
        for layout in layout_names:
            print(f"{layout.upper()+'布局':<15}", end='')
        print("差异")
        print("-" * (25 + 15 * len(layout_names) + 15))
        
        # 比较各项指标
        metrics = [
            ('平均效率(%)', 'avg_efficiency'),
            ('平均得分', 'avg_score'),
            ('Human玩家效率(%)', 'avg_p0_efficiency'),
            ('AI玩家效率(%)', 'avg_p1_efficiency')
        ]
        
        for metric_name, metric_key in metrics:
            print(f"{metric_name:<25}", end='')
            values = []
            for layout in layout_names:
                value = layout_stats[layout][metric_key]
                values.append(value)
                if 'efficiency' in metric_key or metric_key == 'avg_efficiency':
                    print(f"{value:<15.2f}", end='')
                else:
                    print(f"{value:<15.0f}", end='')
            
            # 计算差异
            if len(values) == 2:
                diff = values[0] - values[1]
                print(f"{diff:+.2f}" if 'efficiency' in metric_key or metric_key == 'avg_efficiency' else f"{diff:+.0f}")
            else:
                print("N/A")
    
    return successful_results, layout_stats

def save_results(results, layout_stats):
    """保存分析结果"""
    print(f"\n=== 💾 保存分析结果 ===")
    
    # 保存详细结果
    successful_results = [r for r in results if r['success']]
    if successful_results:
        with open('auto_analysis_detailed.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['filename', 'layout_type', 'total_interactions', 'total_valid', 'total_invalid', 
                         'total_efficiency', 'p0_efficiency', 'p1_efficiency', 'total_score', 
                         'episodes', 'steps_per_episode']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in successful_results:
                row = {field: result.get(field, '') for field in fieldnames}
                writer.writerow(row)
        
        print("✓ 详细结果已保存到: auto_analysis_detailed.csv")
    
    # 保存汇总统计
    if layout_stats:
        summary_data = []
        for layout_type, stats in layout_stats.items():
            summary_data.append({
                'layout_type': layout_type,
                'file_count': stats['file_count'],
                'avg_efficiency': round(stats['avg_efficiency'], 2),
                'avg_score': round(stats['avg_score'], 0),
                'avg_p0_efficiency': round(stats['avg_p0_efficiency'], 2),
                'avg_p1_efficiency': round(stats['avg_p1_efficiency'], 2),
                'total_interactions': stats['total_interactions'],
                'total_valid': stats['total_valid']
            })
        
        with open('auto_analysis_summary.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['layout_type', 'file_count', 'avg_efficiency', 'avg_score', 
                         'avg_p0_efficiency', 'avg_p1_efficiency', 'total_interactions', 'total_valid']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(summary_data)
        
        print("✓ 汇总统计已保存到: auto_analysis_summary.csv")

def main():
    """主函数"""
    print("=== 🤖 自动批量游戏交互分析工具 ===")
    print("自动发现并分析所有数据文件\n")
    
    # 自动发现数据文件
    data_files = find_data_files()
    
    if not data_files:
        print("❌ 未发现任何数据文件")
        return
    
    # 分析所有文件
    results = []
    print(f"\n开始分析 {len(data_files)} 个文件...")
    
    for filename in data_files:
        result = analyze_file(filename)
        results.append(result)
    
    # 生成报告
    successful_results, layout_stats = generate_report(results)
    
    # 保存结果
    save_results(results, layout_stats)
    
    # 失败文件报告
    failed_results = [r for r in results if not r['success']]
    if failed_results:
        print(f"\n⚠️  {len(failed_results)} 个文件分析失败:")
        for r in failed_results:
            print(f"  - {r['filename']}: {r.get('error', 'Unknown error')}")
    
    print(f"\n🎉 分析完成！成功处理 {len(successful_results)}/{len(data_files)} 个文件")

if __name__ == "__main__":
    main()
